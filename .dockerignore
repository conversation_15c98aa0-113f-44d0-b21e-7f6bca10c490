# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
tests/
test-results/
coverage/
junit.xml
*.test.js
*.spec.js

# Documentation
docs/
README.md
*.md

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Linting and formatting
.eslintrc*
.prettierrc*
eslint.config.js
.eslintignore
.prettierignore

# Development tools
nodemon.json
jest.config.js
.husky/
.commitlintrc*

# Build artifacts
dist/
build/
*.tgz
*.tar.gz

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# environment variables file
.env*
src/config/database.json


# Docker
Dockerfile*
docker-compose*
.dockerignore
