<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="9" failures="0" errors="0" time="1.769">
  <testsuite name="AuthController" errors="0" failures="0" skipped="0" timestamp="2025-08-12T05:50:31" time="1.684" tests="9">
    <testcase classname="AuthController POST /api/v1/auth/login successful login should return 200 and auth token for valid credentials" name="AuthController POST /api/v1/auth/login successful login should return 200 and auth token for valid credentials" time="0.145">
    </testcase>
    <testcase classname="AuthController POST /api/v1/auth/login successful login should handle email case normalization" name="AuthController POST /api/v1/auth/login successful login should handle email case normalization" time="0.052">
    </testcase>
    <testcase classname="AuthController POST /api/v1/auth/login validation errors should return 400 for missing email" name="AuthController POST /api/v1/auth/login validation errors should return 400 for missing email" time="0.057">
    </testcase>
    <testcase classname="AuthController POST /api/v1/auth/login validation errors should return 400 for missing password" name="AuthController POST /api/v1/auth/login validation errors should return 400 for missing password" time="0.035">
    </testcase>
    <testcase classname="AuthController POST /api/v1/auth/login validation errors should return 400 for invalid email format" name="AuthController POST /api/v1/auth/login validation errors should return 400 for invalid email format" time="0.033">
    </testcase>
    <testcase classname="AuthController POST /api/v1/auth/login authentication errors should return 404 for non-existent user" name="AuthController POST /api/v1/auth/login authentication errors should return 404 for non-existent user" time="0.035">
    </testcase>
    <testcase classname="AuthController POST /api/v1/auth/login authentication errors should return 400 for invalid password" name="AuthController POST /api/v1/auth/login authentication errors should return 400 for invalid password" time="0.041">
    </testcase>
    <testcase classname="AuthController POST /api/v1/auth/login edge cases should handle empty request body" name="AuthController POST /api/v1/auth/login edge cases should handle empty request body" time="0.035">
    </testcase>
    <testcase classname="AuthController POST /api/v1/auth/login edge cases should handle malformed JSON" name="AuthController POST /api/v1/auth/login edge cases should handle malformed JSON" time="0.02">
    </testcase>
  </testsuite>
</testsuites>