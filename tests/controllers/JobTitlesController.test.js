const api = require('../utils/requestHelper');
const { describeWithTransaction } = require('../utils/describeWithTransaction');
const { UserFactory, JobTitleFactory } = require('../factories');
const { JobTitle } = require('../../src/models');

describeWithTransaction('JobTitlesController', () => {
  let admin;
  let user;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();
  });

  describe('GET /api/v1/job_titles', () => {
    beforeEach(async () => {
      await JobTitleFactory.createMany(3);
    });

    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/job_titles');

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/job_titles');

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });

      it('should return 200 for admin users', async () => {
        const response = await api.as(admin).get('/api/v1/job_titles');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(Array.isArray(response.body.data)).toBe(true);
      });
    });

    describe('successful responses', () => {
      it('should return all job titles with correct format', async () => {
        const response = await api.as(admin).get('/api/v1/job_titles');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data).toHaveLength(3);

        // Check the structure of job title objects
        const jobTitle = response.body.data[0];
        expect(jobTitle).toHaveProperty('id');
        expect(jobTitle).toHaveProperty('name');
        expect(jobTitle).toHaveProperty('prefilled_details');
      });

      it('should return job titles with prefilled details', async () => {
        const se = await JobTitle.findOne();
        await se.update({
          name: 'Software Engineer',
          prefilled_details: {
            department: 'Engineering',
            level: 'Mid-level',
            skills: ['JavaScript', 'Node.js', 'React'],
            experience_required: '2-5 years',
          },
        });

        const response = await api.as(admin).get('/api/v1/job_titles');

        const softwareEngineer = response.body.data.find(jt => jt.name === 'Software Engineer');
        expect(softwareEngineer).toBeDefined();
        expect(softwareEngineer.prefilled_details).toEqual({
          department: 'Engineering',
          level: 'Mid-level',
          skills: ['JavaScript', 'Node.js', 'React'],
          experience_required: '2-5 years',
        });
      });

      it('should return job titles without prefilled details', async () => {
        const sr = await JobTitle.findOne();
        await sr.update({
          name: 'Sales Representative',
          prefilled_details: null,
        });

        const response = await api.as(admin).get('/api/v1/job_titles');

        const salesRep = response.body.data.find(jt => jt.name === 'Sales Representative');
        expect(salesRep).toBeDefined();
        expect(salesRep.prefilled_details).toBeNull();
      });

      it('should include pagination information', async () => {
        const response = await api.as(admin).get('/api/v1/job_titles');

        expect(response.body.pagination).toHaveProperty('page');
        expect(response.body.pagination).toHaveProperty('limit');
        expect(response.body.pagination).toHaveProperty('total');
        expect(response.body.pagination.total).toBe(3);
      });
    });

    describe('pagination and filtering', () => {
      it('should support pagination', async () => {
        const params = { page: 1, limit: 2 };
        const response = await api.as(admin).get('/api/v1/job_titles', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.pagination.page).toBe(1);
        expect(response.body.pagination.limit).toBe(2);
        expect(response.body.pagination.total).toBe(3);
      });

      it('should support search filtering', async () => {
        const pm = await JobTitle.findOne();
        await pm.update({ name: 'Product Manager' });

        const params = { search: 'Product' };
        const response = await api.as(admin).get('/api/v1/job_titles', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].name).toBe('Product Manager');
      });

      it('should support sorting', async () => {
        const lastThree = await JobTitle.findAll({ order: [['name', 'DESC']], limit: 3 });
        await lastThree[0].update({ name: 'Software Engineer' });
        await lastThree[1].update({ name: 'Sales Representative' });
        await lastThree[2].update({ name: 'Product Manager' });

        const params = { sort: 'name', sort_direction: 'desc' };
        const response = await api.as(admin).get('/api/v1/job_titles', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3);
        expect(response.body.data[0].name).toBe('Software Engineer');
        expect(response.body.data[1].name).toBe('Sales Representative');
        expect(response.body.data[2].name).toBe('Product Manager');
      });
    });

    describe('empty results', () => {
      it('should return empty array when no job titles exist', async () => {
        await JobTitle.destroy({ where: {}, truncate: true, cascade: true });
        const response = await api.as(admin).get('/api/v1/job_titles');

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.total).toBe(0);
      });

      it('should return empty array when filter matches nothing', async () => {
        const params = { search: 'NonexistentTitle' };
        const response = await api.as(admin).get('/api/v1/job_titles', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
      });
    });
  });
});
