const api = require('../utils/requestHelper');
const { describeWithTransaction } = require('../utils/describeWithTransaction');
const { UserPosition } = require('../../src/models');
const { UserFactory, UserPositionFactory } = require('../factories');

describeWithTransaction('UserPositionsController', () => {
  let admin;
  let user;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();
  });

  describe('GET /api/v1/user_positions', () => {
    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/user_positions');

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/user_positions');

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });

      it('should return 200 for admin users', async () => {
        const params = { user_id: user.id };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(Array.isArray(response.body.data)).toBe(true);
      });
    });

    describe('successful responses', () => {
      beforeEach(async () => {
        await UserPositionFactory.createMany(2, { user_id: user.id });
      });

      it('should return all user positions with correct format', async () => {
        const params = { user_id: user.id };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data).toHaveLength(2);

        // Check the structure of user position objects
        const userPosition = response.body.data[0];
        expect(userPosition).toHaveProperty('id');
        expect(userPosition).toHaveProperty('role_name');
        expect(userPosition).toHaveProperty('department');
        expect(userPosition).toHaveProperty('starts_at');
        expect(userPosition).toHaveProperty('ends_at');
      });

      it('should include pagination information', async () => {
        const params = { user_id: user.id, page: 1, limit: 1 };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 1,
          total: 2,
        });
      });

      it('should support sorting', async () => {
        const lastTwo = await UserPosition.findAll({ order: [['role_name', 'DESC']], limit: 2 });
        await lastTwo[0].update({ role_name: 'Software Engineer' });
        await lastTwo[1].update({ role_name: 'Senior Software Engineer' });

        const params = { user_id: user.id, sort: 'role_name', sort_direction: 'asc' };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.data[0].role_name).toBe('Senior Software Engineer');
        expect(response.body.data[1].role_name).toBe('Software Engineer');
      });

      it('should return 400 when user_id is not provided in the query', async () => {
        const response = await api.as(admin).get('/api/v1/user_positions');

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');

        const errorDetails = response.body.details;
        expect(errorDetails).toHaveLength(1);
        expect(errorDetails[0]).toHaveProperty('message', "must have required property 'user_id'");
      });

      it('should return 400 for invalid user_id', async () => {
        const params = { user_id: 'invalid' };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return empty array for non-existent user', async () => {
        const params = { user_id: 99999 };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.total).toBe(0);
      });

      it('should handle pagination correctly', async () => {
        const params = { user_id: user.id, page: 1, limit: 1 };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 1,
          total: 2,
        });
      });

      it('should handle invalid pagination parameters', async () => {
        const params = { user_id: user.id, page: 0, limit: 0 };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle limit exceeding maximum', async () => {
        const params = { user_id: user.id, limit: 101 };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle invalid sort parameters', async () => {
        const params = { user_id: user.id, sort: 'invalid_field' };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle invalid sort_direction parameter', async () => {
        const params = { user_id: user.id, sort: 'role_name', sort_direction: 'invalid' };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return positions with all required fields', async () => {
        const params = { user_id: user.id, limit: 1 };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);

        const position = response.body.data[0];
        expect(position).toHaveProperty('id');
        expect(position).toHaveProperty('role_name');
        expect(position).toHaveProperty('department');
        expect(position).toHaveProperty('starts_at');
        expect(position).toHaveProperty('ends_at');
      });

      it('should handle large page numbers gracefully', async () => {
        const params = { user_id: user.id, page: 999 };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.page).toBe(999);
      });

      it('should support different sort fields', async () => {
        const sortFields = ['role_name', 'created_at', 'updated_at'];

        for (const sortField of sortFields) {
          const params = { user_id: user.id, sort: sortField, sort_direction: 'desc' };
          const response = await api.as(admin).get('/api/v1/user_positions', params);

          expect(response.status).toBe(200);
          expect(response.body.data.length).toBeGreaterThan(0);
        }
      });

      it('should handle concurrent requests gracefully', async () => {
        const params = { user_id: user.id };
        const requests = Array.from({ length: 5 }, () =>
          api.as(admin).get('/api/v1/user_positions', params),
        );

        const responses = await Promise.all(requests);

        responses.forEach(response => {
          expect(response.status).toBe(200);
          expect(response.body).toHaveProperty('data');
          expect(response.body).toHaveProperty('pagination');
        });
      });
    });

    describe('edge cases', () => {
      it('should handle empty results when user has no positions', async () => {
        const newUser = await UserFactory.create();
        const params = { user_id: newUser.id };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.total).toBe(0);
      });

      it('should handle special characters in search', async () => {
        const params = { user_id: user.id, search: 'Engineer@#$%' };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
      });

      it('should handle null values in position data', async () => {
        await UserPositionFactory.create({
          user_id: user.id,
          role_name: 'Test Position',
          department: null,
          job_grade: null,
          ends_at: null, // Use the correct database field name
        });

        const params = { user_id: user.id };
        const response = await api.as(admin).get('/api/v1/user_positions', params);

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThan(0);

        const positionWithNulls = response.body.data.find(p => p.role_name === 'Test Position');
        expect(positionWithNulls).toBeTruthy();
        expect(positionWithNulls.department).toBeNull();
        expect(positionWithNulls.ends_at).toBeNull();
      });
    });
  });
});
