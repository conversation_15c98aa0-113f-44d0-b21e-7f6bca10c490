const api = require('../utils/requestHelper');
const { describeWithTransaction } = require('../utils/describeWithTransaction');
const { UserFactory, JobVacancyFactory, JobTitleFactory } = require('../factories');
const { VacancyGroupVariable, JobGroupVariable } = require('../../src/models');

describeWithTransaction('VacancyGroupVariablesController', () => {
  let admin;
  let user;
  let jobVacancy;
  let jobGroupVariable;
  // let vacancyGroupVariable;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();

    // Create test data
    const jobTitle = await JobTitleFactory.create();
    jobVacancy = await JobVacancyFactory.create({ job_title_id: jobTitle.id });

    // Create job group variable
    jobGroupVariable = await JobGroupVariable.create({
      name: 'Test Group Variable',
      description: 'Test description',
      keywords: ['javascript', 'react', 'node'],
      order_level: 1,
    });

    // Create vacancy group variable
    await VacancyGroupVariable.create({
      job_vacancy_id: jobVacancy.id,
      job_group_variable_id: jobGroupVariable.id,
      keyword_match_count: 2,
      keyword_total_count: 3,
      match_type: 'filter',
      weight: null,
      filters: [
        {
          job_variable_id: 1,
          job_variable_name: 'Experience Level',
          value: 'Senior',
        },
      ],
    });
  });

  describe('GET /api/v1/vacancy_group_variables', () => {
    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
        });

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
        });

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });
    });

    describe('validation', () => {
      it('should return 400 when job_vacancy_id is missing', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables');

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when job_vacancy_id is invalid', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: 'invalid',
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });

    describe('successful responses', () => {
      it('should return vacancy group variables with correct format', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
        });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data).toHaveLength(1);

        const vgv = response.body.data[0];
        expect(vgv).toHaveProperty('id');
        expect(vgv).toHaveProperty('job_group_variable');
        expect(vgv.job_group_variable).toHaveProperty('id');
        expect(vgv.job_group_variable).toHaveProperty('name');
        expect(vgv.job_group_variable).toHaveProperty('description');
        expect(vgv).toHaveProperty('keyword_match_count');
        expect(vgv).toHaveProperty('keyword_total_count');
        expect(vgv).toHaveProperty('match_type');
        expect(vgv).toHaveProperty('weight');
        expect(vgv).toHaveProperty('filters');
        expect(vgv).toHaveProperty('order_level');
      });

      it('should support pagination', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
          page: 1,
          limit: 10,
        });

        expect(response.status).toBe(200);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 10,
          total: 1,
        });
      });

      it('should support sorting', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
          sort: 'id',
          sort_direction: 'desc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
      });
    });
  });

  describe('PATCH /api/v1/vacancy_group_variables/bulk_update', () => {
    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.patch('/api/v1/vacancy_group_variables/bulk_update', {
          vacancy_group_variables: [],
        });

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).patch('/api/v1/vacancy_group_variables/bulk_update', {
          vacancy_group_variables: [],
        });

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });
    });

    describe('validation', () => {
      it('should return 400 when vacancy_group_variables is missing', async () => {
        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', {});

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when vacancy_group_variables is empty', async () => {
        const response = await api.as(admin).patch('/api/v1/vacancy_group_variables/bulk_update', {
          vacancy_group_variables: [],
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });

    describe('successful updates', () => {
      it('should update vacancy group variable to weight match type', async () => {
        // Create a fresh record outside of transaction for this test
        const testJobTitle = await JobTitleFactory.create();
        const testJobVacancy = await JobVacancyFactory.create({ job_title_id: testJobTitle.id });
        const testJobGroupVariable = await JobGroupVariable.create({
          name: 'Test Group Variable',
          description: 'Test description',
          keywords: ['javascript', 'react', 'node'],
          order_level: 1,
        });
        const testVacancyGroupVariable = await VacancyGroupVariable.create({
          job_vacancy_id: testJobVacancy.id,
          job_group_variable_id: testJobGroupVariable.id,
          keyword_match_count: 2,
          keyword_total_count: 3,
          match_type: 'filter',
          weight: null,
        });

        const updateData = {
          vacancy_group_variables: [
            {
              id: testVacancyGroupVariable.id,
              match_type: 'weight',
              weight: 0.8,
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', updateData);

        expect(response.status).toBe(200);

        // Verify the update in database
        const updated = await VacancyGroupVariable.findByPk(testVacancyGroupVariable.id);
        expect(updated.match_type).toBe('weight');
        expect(updated.weight).toBe(0.8);
      });

      it('should update vacancy group variable to filter match type', async () => {
        // Create a fresh record outside of transaction for this test
        const testJobTitle = await JobTitleFactory.create();
        const testJobVacancy = await JobVacancyFactory.create({ job_title_id: testJobTitle.id });
        const testJobGroupVariable = await JobGroupVariable.create({
          name: 'Test Group Variable 2',
          description: 'Test description 2',
          keywords: ['python', 'django', 'flask'],
          order_level: 2,
        });
        const testVacancyGroupVariable = await VacancyGroupVariable.create({
          job_vacancy_id: testJobVacancy.id,
          job_group_variable_id: testJobGroupVariable.id,
          keyword_match_count: 1,
          keyword_total_count: 3,
          match_type: 'weight',
          weight: 0.5,
        });

        const updateData = {
          vacancy_group_variables: [
            {
              id: testVacancyGroupVariable.id,
              match_type: 'filter',
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', updateData);

        expect(response.status).toBe(200);

        // Verify the update in database
        const updated = await VacancyGroupVariable.findByPk(testVacancyGroupVariable.id);
        expect(updated.match_type).toBe('filter');
        expect(updated.weight).toBe(0);
      });

      it('should handle bulk update with multiple records', async () => {
        // Create additional test data
        const testJobTitle2 = await JobTitleFactory.create();
        const testJobVacancy2 = await JobVacancyFactory.create({ job_title_id: testJobTitle2.id });
        const testJobGroupVariable2 = await JobGroupVariable.create({
          name: 'Test Group Variable 2',
          description: 'Test description 2',
          keywords: ['python', 'django'],
          order_level: 2,
        });
        const testVacancyGroupVariable2 = await VacancyGroupVariable.create({
          job_vacancy_id: testJobVacancy2.id,
          job_group_variable_id: testJobGroupVariable2.id,
          keyword_match_count: 1,
          keyword_total_count: 2,
          match_type: 'filter',
          weight: null,
          filters: [],
        });

        const updateData = {
          vacancy_group_variables: [
            {
              id: testVacancyGroupVariable2.id,
              match_type: 'weight',
              weight: 0.7,
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', updateData);

        expect(response.status).toBe(200);
      });

      it('should return 400 for invalid vacancy group variable ID', async () => {
        const updateData = {
          vacancy_group_variables: [
            {
              id: 99999, // Non-existent ID
              match_type: 'weight',
              weight: 0.5,
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', updateData);

        expect(response.status).toBe(404);
        expect(response.body).toHaveProperty('error');
      });

      it('should return 400 when weight is missing for weight match type', async () => {
        const testJobTitle = await JobTitleFactory.create();
        const testJobVacancy = await JobVacancyFactory.create({ job_title_id: testJobTitle.id });
        const testJobGroupVariable = await JobGroupVariable.create({
          name: 'Test Group Variable',
          description: 'Test description',
          keywords: ['test'],
          order_level: 1,
        });
        const testVacancyGroupVariable = await VacancyGroupVariable.create({
          job_vacancy_id: testJobVacancy.id,
          job_group_variable_id: testJobGroupVariable.id,
          keyword_match_count: 1,
          keyword_total_count: 1,
          match_type: 'filter',
          weight: null,
          filters: [],
        });

        const updateData = {
          vacancy_group_variables: [
            {
              id: testVacancyGroupVariable.id,
              match_type: 'weight',
              // Missing weight
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', updateData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error');
      });
    });

    describe('edge cases', () => {
      it('should handle empty filters array in GET request', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
          limit: 1,
        });

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
      });

      it('should handle large page numbers in GET request', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
          page: 999,
        });

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.page).toBe(999);
      });

      it('should handle invalid sort parameters in GET request', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
          sort: 'invalid_field',
        });

        expect(response.status).toBe(400); // Should return validation error for invalid sort field
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });
  });
});
