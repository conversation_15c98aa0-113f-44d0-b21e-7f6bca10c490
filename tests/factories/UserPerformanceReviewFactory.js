'use strict';

const BaseFactory = require('./BaseFactory');
const { UserPerformanceReview } = require('../../src/models');

class UserPerformanceReviewFactory extends BaseFactory {
  constructor() {
    super(UserPerformanceReview);
  }

  static defaultAttributes() {
    return {
      user_position_id: this.faker.number.int({ min: 1, max: 100 }),
      review_type: this.faker.helpers.arrayElement([
        'Annual Review',
        'Mid-Year Review',
        'Quarterly Review',
        'Probation Review',
        '360 Degree Review',
        'Performance Improvement Plan',
      ]),
      review_result: {
        overall_rating: this.faker.number.float({ min: 1, max: 5, precision: 0.1 }),
        strengths: [
          this.faker.helpers.arrayElement([
            'Strong communication skills',
            'Excellent problem-solving abilities',
            'Great team collaboration',
            'Leadership potential',
            'Technical expertise',
          ]),
        ],
        areas_for_improvement: [
          this.faker.helpers.arrayElement([
            'Time management',
            'Attention to detail',
            'Public speaking',
            'Project planning',
            'Delegation skills',
          ]),
        ],
        goals: [
          this.faker.helpers.arrayElement([
            'Complete certification program',
            'Lead a major project',
            'Improve customer satisfaction scores',
            'Mentor junior team members',
            'Develop new skills',
          ]),
        ],
        reviewer_comments: this.faker.lorem.paragraph(),
        employee_comments: this.faker.lorem.paragraph(),
      },
      createdAt: this.faker.date.recent({ days: 30 }),
      updatedAt: this.faker.date.recent({ days: 30 }),
    };
  }
}

module.exports = UserPerformanceReviewFactory;
