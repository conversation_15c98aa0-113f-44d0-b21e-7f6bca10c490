'use strict';

const BaseFactory = require('./BaseFactory');
const { JobLevel } = require('../../src/models');

class JobLevelFactory extends BaseFactory {
  constructor() {
    super(JobLevel);
  }

  static defaultAttributes() {
    const levels = [
      'Entry Level',
      'Junior Level',
      'Mid Level',
      'Senior Level',
      'Lead Level',
      'Principal Level',
      'Director Level',
      'VP Level',
      'C-Level',
    ];

    return {
      name: this.faker.helpers.arrayElement(levels),
      order_level: this.faker.number.int({ min: 1, max: 9 }),
    };
  }
}

module.exports = JobLevelFactory;
