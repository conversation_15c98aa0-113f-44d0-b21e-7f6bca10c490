'use strict';

const BaseFactory = require('./BaseFactory');
const { UserJobVacancy } = require('../../src/models');

class UserJobVacancyFactory extends BaseFactory {
  constructor() {
    super(UserJobVacancy);
  }

  static defaultAttributes() {
    return {
      competency_match: this.faker.number.float({ min: 0, max: 1, fractionDigits: 2 }),
      skill_match: this.faker.number.float({ min: 0, max: 1, fractionDigits: 2 }),
      status: this.faker.helpers.arrayElement([
        'matched',
        'shortlisted',
        'approved',
        'appointed',
        'not_matched',
      ]),
    };
  }

  static traits() {
    return {
      matched: {
        status: 'matched',
        competency_match: this.faker.number.float({ min: 0.7, max: 1, fractionDigits: 2 }),
        skill_match: this.faker.number.float({ min: 0.7, max: 1, fractionDigits: 2 }),
      },
      not_matched: {
        status: 'not_matched',
        competency_match: this.faker.number.float({ min: 0, max: 0.5, fractionDigits: 2 }),
        skill_match: this.faker.number.float({ min: 0, max: 0.5, fractionDigits: 2 }),
      },
      shortlisted: {
        status: 'shortlisted',
        competency_match: this.faker.number.float({ min: 0.8, max: 1, fractionDigits: 2 }),
        skill_match: this.faker.number.float({ min: 0.8, max: 1, fractionDigits: 2 }),
      },
      approved: {
        status: 'approved',
        competency_match: this.faker.number.float({ min: 0.9, max: 1, fractionDigits: 2 }),
        skill_match: this.faker.number.float({ min: 0.9, max: 1, fractionDigits: 2 }),
      },
      appointed: {
        status: 'appointed',
        competency_match: this.faker.number.float({ min: 0.95, max: 1, fractionDigits: 2 }),
        skill_match: this.faker.number.float({ min: 0.95, max: 1, fractionDigits: 2 }),
      },
    };
  }
}

module.exports = UserJobVacancyFactory;
