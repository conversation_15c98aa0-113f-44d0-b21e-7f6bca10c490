'use strict';

const BaseFactory = require('./BaseFactory');
const { WorkArea } = require('../../src/models');

class WorkAreaFactory extends BaseFactory {
  constructor() {
    super(WorkArea);
  }

  static defaultAttributes() {
    const workAreas = [
      'Engineering',
      'Product Management',
      'Design',
      'Marketing',
      'Sales',
      'Human Resources',
      'Finance',
      'Operations',
      'Customer Success',
      'Data Science',
      'Quality Assurance',
      'DevOps',
      'Business Development',
      'Legal',
      'Research & Development',
    ];

    return {
      name: this.faker.helpers.arrayElement(workAreas),
    };
  }

  static traits() {
    return {
      engineering: {
        name: 'Engineering',
      },
      product: {
        name: 'Product Management',
      },
      design: {
        name: 'Design',
      },
      marketing: {
        name: 'Marketing',
      },
    };
  }
}

module.exports = WorkAreaFactory;
