'use strict';

const BaseFactory = require('./BaseFactory');
const { JobTitle } = require('../../src/models');

class JobTitleFactory extends BaseFactory {
  constructor() {
    super(JobTitle);
  }

  static defaultAttributes() {
    const jobTitle = this.faker.person.jobTitle();
    const department = this.faker.person.jobArea();

    return {
      name: jobTitle,
      prefilled_details: {
        department,
        level: this.faker.helpers.arrayElement(['L1', 'L2', 'L3', 'L4', 'L5', 'L6', 'L7']),
        skills: Array.from({ length: 3 }, (_, i) => `${this.faker.hacker.verb()} ${i + 1}`),
        competencies: Array.from({ length: 4 }, (_, i) => `${this.faker.hacker.verb()} ${i + 1}`),
        description: this.faker.lorem.paragraph(),
      },
    };
  }
}

module.exports = JobTitleFactory;
