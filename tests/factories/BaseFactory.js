'use strict';

const { faker } = require('@faker-js/faker');

/**
 * BaseFactory - Base class for all model factories
 * Provides common functionality for creating test data with faker integration
 */
class BaseFactory {
  constructor(model) {
    this.model = model;
  }

  /**
   * Define default attributes for the model
   * Should be overridden by child classes
   * @returns {Object} Default attributes
   */
  static defaultAttributes() {
    return {};
  }

  /**
   * Create a single instance with optional custom attributes
   * @param {Object} customAttributes - Custom attributes to override defaults
   * @param {Object} options - Sequelize create options
   * @returns {Promise<Object>} Created model instance
   */
  static async create(customAttributes = {}, options = {}) {
    const factory = new this();
    const attributes = {
      ...factory.constructor.defaultAttributes(),
      ...customAttributes,
    };

    return await factory.model.create(attributes, options);
  }

  /**
   * Create multiple instances
   * @param {number} count - Number of instances to create
   * @param {Object} customAttributes - Custom attributes to override defaults (applied to all)
   * @param {Object} options - Sequelize bulkCreate options
   * @returns {Promise<Array>} Array of created model instances
   */
  static async createMany(count, customAttributes = {}, options = {}) {
    const factory = new this();
    const instances = factory.constructor.buildMany(count, customAttributes);

    // Use individual creates instead of bulkCreate to ensure hooks run properly
    const createdInstances = [];
    for (const instanceData of instances) {
      const created = await factory.model.create(instanceData, options);
      createdInstances.push(created);
    }

    return createdInstances;
  }

  /**
   * Build attributes without creating the instance
   * Useful for testing validations or getting data for API calls
   * @param {Object} customAttributes - Custom attributes to override defaults
   * @returns {Object} Built attributes
   */
  static build(customAttributes = {}) {
    const factory = new this();
    return {
      ...factory.constructor.defaultAttributes(),
      ...customAttributes,
    };
  }

  /**
   * Build multiple sets of attributes without creating instances
   * @param {number} count - Number of attribute sets to build
   * @param {Object} customAttributes - Custom attributes to override defaults (applied to all)
   * @returns {Array<Object>} Array of built attributes
   */
  static buildMany(count, customAttributes = {}) {
    const factory = new this();
    const attributeSets = [];

    for (let i = 0; i < count; i++) {
      // Add a small delay to ensure unique timestamps for email generation
      const baseAttributes = factory.constructor.defaultAttributes();

      // If this is a UserFactory and we're generating emails, ensure uniqueness
      if (
        baseAttributes.email &&
        typeof baseAttributes.email === 'string' &&
        baseAttributes.email.includes('user_')
      ) {
        // Force a unique timestamp by adding the index
        const timestamp = Date.now() + i;
        const random = Math.floor(Math.random() * 1000);
        const domain = faker.internet.domainName();
        baseAttributes.email = `user_${timestamp}_${random}@${domain}`;
      }

      const attributes = {
        ...baseAttributes,
        ...customAttributes,
      };
      attributeSets.push(attributes);
    }

    return attributeSets;
  }

  /**
   * Create instances with specific traits/states
   * @param {string} trait - The trait name
   * @param {Object} customAttributes - Custom attributes to override defaults
   * @param {Object} options - Sequelize create options
   * @returns {Promise<Object>} Created model instance
   */
  static async createWithTrait(trait, customAttributes = {}, options = {}) {
    const factory = new this();
    const traitAttributes = factory.constructor.traits()[trait] || {};
    const attributes = {
      ...factory.constructor.defaultAttributes(),
      ...traitAttributes,
      ...customAttributes,
    };

    return await factory.model.create(attributes, options);
  }

  /**
   * Define traits for different states/variations of the model
   * Should be overridden by child classes
   * @returns {Object} Object with trait names as keys and attribute objects as values
   */
  static traits() {
    return {};
  }

  /**
   * Helper method to get a unique email using faker
   * @param {string} prefix - Optional prefix for the email
   * @returns {string} Unique email address
   */
  static uniqueEmail(prefix = '') {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    const domain = faker.internet.domainName();
    return prefix
      ? `${prefix}_${timestamp}_${random}@${domain}`
      : `user_${timestamp}_${random}@${domain}`;
  }

  /**
   * Helper method to get faker instance for use in child classes
   * @returns {Object} Faker instance
   */
  static get faker() {
    return faker;
  }
}

module.exports = BaseFactory;
