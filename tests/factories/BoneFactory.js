'use strict';

const BaseFactory = require('./BaseFactory');
const { Bone } = require('../../src/models');

class BoneFactory extends BaseFactory {
  constructor() {
    super(Bone);
  }

  static defaultAttributes() {
    const boneTypes = [
      'Technical',
      'Leadership',
      'Communication',
      'Problem Solving',
      'Innovation',
      'Collaboration',
      'Strategic Thinking',
      'Customer Focus',
      'Adaptability',
      'Results Orientation',
    ];

    return {
      name: this.faker.helpers.arrayElement(boneTypes),
    };
  }

  static traits() {
    return {
      technical: {
        name: 'Technical',
      },
      leadership: {
        name: 'Leadership',
      },
      communication: {
        name: 'Communication',
      },
    };
  }
}

module.exports = BoneFactory;
