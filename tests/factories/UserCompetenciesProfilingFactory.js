'use strict';

const BaseFactory = require('./BaseFactory');
const { UserCompetenciesProfiling } = require('../../src/models');

class UserCompetenciesProfilingFactory extends BaseFactory {
  constructor() {
    super(UserCompetenciesProfiling);
  }

  static defaultAttributes() {
    return {
      user_id: this.faker.number.int({ min: 1, max: 100 }),
      profiling_date: this.faker.date.recent({ days: 30 }).toISOString().split('T')[0],
      assessors: [this.faker.person.fullName(), this.faker.person.fullName()],
      profile_as: this.faker.helpers.arrayElement([
        'Individual Contributor',
        'Team Lead',
        'Manager',
        'Senior Manager',
        'Director',
        'VP',
        'C-Level',
      ]),
      readiness: this.faker.helpers.arrayElement([
        'Ready Now',
        'Ready in 6 months',
        'Ready in 1 year',
        'Ready in 2+ years',
        'Not Ready',
      ]),
      metadata: {
        competency_scores: {
          leadership: this.faker.number.float({ min: 1, max: 5, precision: 0.1 }),
          communication: this.faker.number.float({ min: 1, max: 5, precision: 0.1 }),
          technical_skills: this.faker.number.float({ min: 1, max: 5, precision: 0.1 }),
          problem_solving: this.faker.number.float({ min: 1, max: 5, precision: 0.1 }),
          teamwork: this.faker.number.float({ min: 1, max: 5, precision: 0.1 }),
        },
        assessment_method: this.faker.helpers.arrayElement([
          '360 Degree Feedback',
          'Manager Assessment',
          'Self Assessment',
          'Peer Review',
          'Skills Test',
        ]),
        notes: this.faker.lorem.paragraph(),
        development_areas: [
          this.faker.helpers.arrayElement([
            'Strategic Thinking',
            'People Management',
            'Technical Leadership',
            'Communication Skills',
            'Decision Making',
          ]),
        ],
      },
      createdAt: this.faker.date.recent({ days: 30 }).toISOString().split('T')[0],
      updatedAt: this.faker.date.recent({ days: 30 }).toISOString().split('T')[0],
    };
  }
}

module.exports = UserCompetenciesProfilingFactory;
