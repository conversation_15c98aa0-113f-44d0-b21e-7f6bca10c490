'use strict';

const BaseFactory = require('./BaseFactory');
const { JobVacancy } = require('../../src/models');

class JobVacancyFactory extends BaseFactory {
  constructor() {
    super(JobVacancy);
  }

  static defaultAttributes() {
    const department = this.faker.person.jobArea();
    const jobTitle = this.faker.person.jobTitle();

    return {
      name: jobTitle,
      department,
      job_grade: this.faker.helpers.arrayElement(['L1', 'L2', 'L3', 'L4', 'L5', 'L6', 'L7']),
      job_description: this.faker.lorem.paragraphs(3, '\n\n'),
      competencies: Array.from({ length: 4 }, (_, i) => `${this.faker.hacker.verb()} ${i + 1}`),
      skills: Array.from({ length: 3 }, (_, i) => `${this.faker.hacker.verb()} ${i + 1}`),
    };
  }
}

module.exports = JobVacancyFactory;
