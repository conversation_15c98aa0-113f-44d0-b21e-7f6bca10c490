'use strict';

const BaseFactory = require('./BaseFactory');
const { UserProfile } = require('../../src/models');

class UserProfileFactory extends BaseFactory {
  constructor() {
    super(UserProfile);
  }

  static defaultAttributes() {
    return {
      phone_number: this.faker.phone.number(),
      location: this.faker.location.city(),
      manager: this.faker.person.fullName(),
      current_position: {
        title: this.faker.person.jobTitle(),
        department: this.faker.person.jobArea(),
        level: this.faker.helpers.arrayElement(['L1', 'L2', 'L3', 'L4', 'L5', 'L6', 'L7']),
      },
      years_experience: this.faker.number.int({ min: 0, max: 30 }),
      performance_rating: this.faker.number.float({ min: 1.0, max: 5.0, fractionDigits: 1 }),
      last_promotion: this.faker.date.past({ years: 3 }),
      education: this.faker.hacker.noun(),
      competencies: Array.from({ length: 5 }, (_, i) => `${this.faker.hacker.verb()} ${i + 1}`),
      skills: Array.from({ length: 3 }, (_, i) => `${this.faker.hacker.verb()} ${i + 1}`),
    };
  }
}

module.exports = UserProfileFactory;
