'use strict';

const fs = require('fs');
const path = require('path');
const factories = {};

fs.readdirSync(__dirname)
  .filter(file => {
    return file.indexOf('.') !== 0 && file !== 'index.js' && file.slice(-3) === '.js';
  })
  .forEach(file => {
    const factory = require(path.join(__dirname, file));
    const factoryName = path.basename(file, '.js');
    factories[factoryName] = factory;
  });

module.exports = factories;
