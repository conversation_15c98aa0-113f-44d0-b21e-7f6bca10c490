'use strict';

const BaseFactory = require('./BaseFactory');
const { UserAssessmentResult } = require('../../src/models');

class UserAssessmentResultFactory extends BaseFactory {
  constructor() {
    super(UserAssessmentResult);
  }

  static defaultAttributes() {
    return {
      user_id: this.faker.number.int({ min: 1, max: 100 }),
      assessment: this.faker.helpers.arrayElement([
        'Personality Assessment',
        'Skills Assessment',
        'Cognitive Assessment',
        'Leadership Assessment',
        'Technical Assessment',
      ]),
      aspect_name: this.faker.helpers.arrayElement([
        'Communication',
        'Problem Solving',
        'Leadership',
        'Technical Skills',
        'Teamwork',
        'Adaptability',
        'Critical Thinking',
      ]),
      value_type: this.faker.helpers.arrayElement([
        'score',
        'percentage',
        'rating',
        'level',
        'grade',
      ]),
      value: this.faker.helpers.arrayElement([
        this.faker.number.int({ min: 1, max: 100 }).toString(),
        this.faker.helpers.arrayElement(['A', 'B', 'C', 'D', 'F']),
        this.faker.helpers.arrayElement(['Excellent', 'Good', 'Average', 'Poor']),
        this.faker.helpers.arrayElement(['Beginner', 'Intermediate', 'Advanced', 'Expert']),
      ]),
      createdAt: this.faker.date.recent({ days: 30 }),
      updatedAt: this.faker.date.recent({ days: 30 }),
    };
  }
}

module.exports = UserAssessmentResultFactory;
