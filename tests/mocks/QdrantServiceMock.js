/**
 * Mock for Qdrant client to avoid connection failures during testing
 */
class QdrantServiceMock {
  constructor() {
    this.searchBatchMock = jest.fn();
    this.retrieveMock = jest.fn();
    this.searchMock = jest.fn();
  }

  /**
   * Mock searchBatch method
   * @param {string} collectionName - Collection name
   * @param {Object} params - Search parameters
   * @returns {Array} Mocked search results
   */
  async searchBatch(collectionName, _params) {
    const defaultOnetResults = [
      [
        { id: 'onet_1', score: 0.95 },
        { id: 'onet_2', score: 0.87 },
        { id: 'onet_3', score: 0.82 },
      ],
      [
        { id: 'onet_4', score: 0.91 },
        { id: 'onet_5', score: 0.85 },
        { id: 'onet_6', score: 0.79 },
      ],
    ];

    const defaultInternalResults = [
      [
        { id: 'internal_1', score: 0.93 },
        { id: 'internal_2', score: 0.88 },
        { id: 'internal_3', score: 0.84 },
      ],
      [
        { id: 'internal_4', score: 0.89 },
        { id: 'internal_5', score: 0.86 },
        { id: 'internal_6', score: 0.81 },
      ],
    ];

    // Return appropriate mock based on collection name
    if (collectionName === 'onet_job_title') {
      return this.searchBatchMock.mockReturnValue(defaultOnetResults)();
    } else if (collectionName === 'internal_job_title') {
      return this.searchBatchMock.mockReturnValue(defaultInternalResults)();
    }

    // Default fallback
    return this.searchBatchMock.mockReturnValue(defaultOnetResults)();
  }

  /**
   * Mock retrieve method
   * @param {string} collectionName - Collection name
   * @param {Object} params - Retrieve parameters
   * @returns {Array} Mocked retrieve results
   */
  async retrieve(collectionName, _params) {
    const defaultOnetData = [
      {
        id: 'onet_1',
        payload: {
          onetsoc_code: '15-1132.00',
          job_title: 'Software Developer',
        },
      },
      {
        id: 'onet_2',
        payload: {
          onetsoc_code: '15-1133.00',
          job_title: 'Software Engineer',
        },
      },
      {
        id: 'onet_3',
        payload: {
          onetsoc_code: '15-1134.00',
          job_title: 'Web Developer',
        },
      },
    ];

    const defaultInternalData = [
      {
        id: 'internal_1',
        payload: {
          table_internal_id: 1,
          job_title: 'Senior Software Developer',
        },
      },
      {
        id: 'internal_2',
        payload: {
          table_internal_id: 2,
          job_title: 'Full Stack Developer',
        },
      },
      {
        id: 'internal_3',
        payload: {
          table_internal_id: 3,
          job_title: 'Frontend Developer',
        },
      },
    ];

    // Return appropriate mock based on collection name
    if (collectionName === 'onet_job_title') {
      return this.retrieveMock.mockReturnValue(defaultOnetData)();
    } else if (collectionName === 'internal_job_title') {
      return this.retrieveMock.mockReturnValue(defaultInternalData)();
    }

    // Default fallback
    return this.retrieveMock.mockReturnValue(defaultOnetData)();
  }

  /**
   * Mock search method (for single searches)
   * @param {string} collectionName - Collection name
   * @param {Object} params - Search parameters
   * @returns {Array} Mocked search results
   */
  async search(_collectionName, _params) {
    const defaultResults = [
      {
        id: 'result_1',
        score: 0.95,
        payload: {
          onetsoc_code: '15-1132.00',
          job_title: 'Software Developer',
        },
      },
      {
        id: 'result_2',
        score: 0.87,
        payload: {
          onetsoc_code: '15-1133.00',
          job_title: 'Software Engineer',
        },
      },
    ];

    return this.searchMock.mockReturnValue(defaultResults)();
  }

  /**
   * Set custom mock response for searchBatch
   * @param {Array} response - Custom response array
   */
  mockSearchBatch(response) {
    this.searchBatchMock.mockResolvedValue(response);
  }

  /**
   * Set custom mock response for retrieve
   * @param {Array} response - Custom response array
   */
  mockRetrieve(response) {
    this.retrieveMock.mockResolvedValue(response);
  }

  /**
   * Set custom mock response for search
   * @param {Array} response - Custom response array
   */
  mockSearch(response) {
    this.searchMock.mockResolvedValue(response);
  }

  /**
   * Mock connection error
   * @param {string} message - Error message
   */
  mockConnectionError(message = 'connect ECONNREFUSED 127.0.0.1:6333') {
    const error = new Error('fetch failed');
    error.cause = new Error(message);
    this.searchBatchMock.mockRejectedValue(error);
    this.retrieveMock.mockRejectedValue(error);
    this.searchMock.mockRejectedValue(error);
  }

  /**
   * Reset all mocks
   */
  resetMocks() {
    this.searchBatchMock.mockReset();
    this.retrieveMock.mockReset();
    this.searchMock.mockReset();
  }
}

module.exports = QdrantServiceMock;
