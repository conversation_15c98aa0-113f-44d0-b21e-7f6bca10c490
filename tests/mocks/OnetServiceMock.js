/**
 * Mock for OnetService to avoid database queries during testing
 */
class OnetServiceMock {
  constructor() {
    this.getOccupationsMock = jest.fn();
    this.getSkillsMock = jest.fn();
    this.getAbilitiesMock = jest.fn();
    this.getWorkValuesMock = jest.fn();
    this.getTasksMock = jest.fn();
  }

  /**
   * Mock getOccupations method
   * @param {Array} onetsocCodes - Array of O*NET SOC codes
   * @returns {Object} Mocked occupations data
   */
  async getOccupations(_onetsocCodes) {
    const defaultOccupations = {
      '15-1252.00': {
        title: 'Software Developers, Applications',
        description:
          'Develop, create, and modify general computer applications software or specialized utility programs.',
      },
      '15-1253.00': {
        title: 'Software Developers, Systems Software',
        description:
          'Research, design, develop, and test operating systems-level software, compilers, and network distribution software.',
      },
      '15-1254.00': {
        title: 'Web Developers',
        description:
          'Develop and implement websites, web applications, application databases, and interactive web interfaces.',
      },
    };

    return this.getOccupationsMock.mockReturnValue(defaultOccupations)();
  }

  /**
   * Mock getSkills method
   * @param {Array} onetsocCodes - Array of O*NET SOC codes
   * @returns {Array} Mocked skills data
   */
  async getSkills(_onetsocCodes) {
    const defaultSkills = [
      {
        onetsoc_code: '15-1252.00',
        skill: 'Programming',
        description: 'Writing computer programs for various purposes.',
        importance: 85.5,
        level_required: 75.2,
      },
      {
        onetsoc_code: '15-1252.00',
        skill: 'Systems Analysis',
        description:
          'Determining how a system should work and how changes in conditions, operations, and the environment will affect outcomes.',
        importance: 78.3,
        level_required: 70.1,
      },
      {
        onetsoc_code: '15-1252.00',
        skill: 'Critical Thinking',
        description:
          'Using logic and reasoning to identify the strengths and weaknesses of alternative solutions.',
        importance: 82.1,
        level_required: 72.8,
      },
    ];

    return this.getSkillsMock.mockReturnValue(defaultSkills)();
  }

  /**
   * Mock getAbilities method
   * @param {Array} onetsocCodes - Array of O*NET SOC codes
   * @returns {Array} Mocked abilities data
   */
  async getAbilities(_onetsocCodes) {
    const defaultAbilities = [
      {
        onetsoc_code: '15-1252.00',
        ability: 'Deductive Reasoning',
        description:
          'The ability to apply general rules to specific problems to produce answers that make sense.',
        importance: 88.2,
        level_required: 78.5,
      },
      {
        onetsoc_code: '15-1252.00',
        ability: 'Information Ordering',
        description:
          'The ability to arrange things or actions in a certain order or pattern according to a specific rule or set of rules.',
        importance: 85.7,
        level_required: 75.3,
      },
      {
        onetsoc_code: '15-1252.00',
        ability: 'Problem Sensitivity',
        description: 'The ability to tell when something is wrong or is likely to go wrong.',
        importance: 82.4,
        level_required: 73.1,
      },
    ];

    return this.getAbilitiesMock.mockReturnValue(defaultAbilities)();
  }

  /**
   * Mock getWorkValues method
   * @param {Array} onetsocCodes - Array of O*NET SOC codes
   * @returns {Array} Mocked work values data
   */
  async getWorkValues(_onetsocCodes) {
    const defaultWorkValues = [
      {
        onetsoc_code: '15-1252.00',
        work_value: 'Achievement',
        description:
          'Occupations that satisfy this work value are results oriented and allow employees to use their strongest abilities.',
        extent: 85.2,
      },
      {
        onetsoc_code: '15-1252.00',
        work_value: 'Working Conditions',
        description:
          'Occupations that satisfy this work value offer job security and good working conditions.',
        extent: 78.9,
      },
      {
        onetsoc_code: '15-1252.00',
        work_value: 'Independence',
        description:
          'Occupations that satisfy this work value allow employees to work on their own and make decisions.',
        extent: 82.1,
      },
    ];

    return this.getWorkValuesMock.mockReturnValue(defaultWorkValues)();
  }

  /**
   * Mock getTasks method
   * @param {Array} onetsocCodes - Array of O*NET SOC codes
   * @returns {Array} Mocked tasks data
   */
  async getTasks(_onetsocCodes) {
    const defaultTasks = [
      {
        task: 'Analyze user needs and software requirements to determine feasibility of design within time and cost constraints.',
        task_type: 'Core',
        importance: 88.5,
      },
      {
        task: 'Design, develop and modify software systems, using scientific analysis and mathematical models to predict and measure outcome and consequences of design.',
        task_type: 'Core',
        importance: 85.7,
      },
      {
        task: 'Develop and direct software system testing and validation procedures, programming, and documentation.',
        task_type: 'Core',
        importance: 82.3,
      },
    ];

    return this.getTasksMock.mockReturnValue(defaultTasks)();
  }

  /**
   * Set custom mock response for getOccupations
   * @param {Object} response - Custom response object
   */
  mockGetOccupations(response) {
    this.getOccupationsMock.mockResolvedValue(response);
  }

  /**
   * Set custom mock response for getSkills
   * @param {Array} response - Custom response array
   */
  mockGetSkills(response) {
    this.getSkillsMock.mockResolvedValue(response);
  }

  /**
   * Set custom mock response for getAbilities
   * @param {Array} response - Custom response array
   */
  mockGetAbilities(response) {
    this.getAbilitiesMock.mockResolvedValue(response);
  }

  /**
   * Set custom mock response for getWorkValues
   * @param {Array} response - Custom response array
   */
  mockGetWorkValues(response) {
    this.getWorkValuesMock.mockResolvedValue(response);
  }

  /**
   * Set custom mock response for getTasks
   * @param {Array} response - Custom response array
   */
  mockGetTasks(response) {
    this.getTasksMock.mockResolvedValue(response);
  }

  /**
   * Mock database error
   * @param {string} message - Error message
   */
  mockError(message = 'Database connection error') {
    const error = new Error(message);
    this.getOccupationsMock.mockRejectedValue(error);
    this.getSkillsMock.mockRejectedValue(error);
    this.getAbilitiesMock.mockRejectedValue(error);
    this.getWorkValuesMock.mockRejectedValue(error);
    this.getTasksMock.mockRejectedValue(error);
  }

  /**
   * Reset all mocks
   */
  resetMocks() {
    this.getOccupationsMock.mockReset();
    this.getSkillsMock.mockReset();
    this.getAbilitiesMock.mockReset();
    this.getWorkValuesMock.mockReset();
    this.getTasksMock.mockReset();
  }
}

module.exports = OnetServiceMock;
