/**
 * Mock for GoogleAiService to avoid expensive API calls during testing
 */
class GoogleAiServiceMock {
  constructor() {
    this.generateContentMock = jest.fn();
    this.embedContentMock = jest.fn();

    // Structure to match GoogleGenAI expected interface
    this.models = {
      generateContent: this.generateContent.bind(this),
      embedContent: this.embedContent.bind(this),
    };
  }

  /**
   * Mock generateContent method
   * @param {Object} params - Generation parameters
   * @returns {Object} Mocked response
   */
  async generateContent(params) {
    // Default mock response for job description generation
    const defaultJobDescResponse = {
      candidates: [
        {
          content: {
            parts: [
              {
                text: JSON.stringify({
                  key_responsibilities: [
                    'Develop and maintain software applications',
                    'Collaborate with cross-functional teams',
                    'Write clean, maintainable code',
                    'Participate in code reviews',
                  ],
                  qualifications: [
                    "Bachelor's degree in Computer Science or related field",
                    '3+ years of software development experience',
                    'Proficiency in JavaScript, Node.js, and React',
                    'Strong problem-solving skills',
                  ],
                  preferred_qualifications: [
                    'Experience with cloud platforms (AWS, GCP, Azure)',
                    'Knowledge of database systems',
                    'Agile development experience',
                  ],
                  benefits: [
                    'Competitive salary and benefits',
                    'Health insurance coverage',
                    'Professional development opportunities',
                    'Flexible work arrangements',
                  ],
                }),
              },
            ],
          },
        },
      ],
    };

    // Default mock response for KSAO generation
    const defaultKsaoResponse = {
      candidates: [
        {
          content: {
            parts: [
              {
                text: JSON.stringify({
                  skills: [
                    'JavaScript Programming',
                    'React Development',
                    'Node.js',
                    'Database Management',
                    'Problem Solving',
                  ],
                  abilities: [
                    'Analytical Thinking',
                    'Attention to Detail',
                    'Communication',
                    'Teamwork',
                    'Learning Agility',
                  ],
                  knowledges: [
                    'Software Engineering Principles',
                    'Web Development Technologies',
                    'Database Systems',
                    'Version Control Systems',
                    'Testing Methodologies',
                  ],
                  other_characteristics: [
                    'Adaptability',
                    'Initiative',
                    'Reliability',
                    'Creativity',
                    'Time Management',
                  ],
                }),
              },
            ],
          },
        },
      ],
    };

    // Default mock response for related job titles
    const defaultRelatedTitlesResponse = {
      candidates: [
        {
          content: {
            parts: [
              {
                text: JSON.stringify([
                  'Frontend Developer',
                  'Full Stack Developer',
                  'Software Engineer',
                ]),
              },
            ],
          },
        },
      ],
    };

    // Return appropriate mock based on the model or content
    if (params.model === 'gemini-2.5-pro') {
      return this.generateContentMock.mockReturnValue(defaultJobDescResponse)();
    } else if (params.contents?.[0]?.parts?.[0]?.text?.includes('related job titles')) {
      return this.generateContentMock.mockReturnValue(defaultRelatedTitlesResponse)();
    } else if (params.model === 'gemini-2.5-flash') {
      return this.generateContentMock.mockReturnValue(defaultKsaoResponse)();
    }

    // Default fallback
    return this.generateContentMock.mockReturnValue(defaultJobDescResponse)();
  }

  /**
   * Mock embedContent method
   * @param {Object} params - Embedding parameters
   * @returns {Object} Mocked embedding response
   */
  async embedContent(params) {
    const contents = params.contents || ['default'];
    const defaultEmbeddingResponse = {
      embeddings: contents.map(() => ({
        values: Array.from({ length: 768 }, () => Math.random() * 2 - 1), // Random embeddings
      })),
    };

    return this.embedContentMock.mockReturnValue(defaultEmbeddingResponse)();
  }

  /**
   * Set custom mock response for generateContent
   * @param {Object} response - Custom response object
   */
  mockGenerateContent(response) {
    this.generateContentMock.mockResolvedValue(response);
  }

  /**
   * Set custom mock response for embedContent
   * @param {Object} response - Custom response object
   */
  mockEmbedContent(response) {
    this.embedContentMock.mockResolvedValue(response);
  }

  /**
   * Mock API error responses
   * @param {number} status - HTTP status code
   * @param {string} message - Error message
   */
  mockError(status = 503, message = 'Service temporarily unavailable') {
    const error = new Error(message);
    error.status = status;
    this.generateContentMock.mockRejectedValue(error);
    this.embedContentMock.mockRejectedValue(error);
  }

  /**
   * Reset all mocks
   */
  resetMocks() {
    this.generateContentMock.mockReset();
    this.embedContentMock.mockReset();
  }
}

module.exports = GoogleAiServiceMock;
