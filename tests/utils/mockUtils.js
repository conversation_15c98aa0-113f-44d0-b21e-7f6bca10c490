const GoogleAiServiceMock = require('../mocks/GoogleAiServiceMock');
const OnetServiceMock = require('../mocks/OnetServiceMock');

/**
 * Utility functions for mocking external services in tests
 */
class MockUtils {
  static googleAiServiceMock = null;
  static onetServiceMock = null;

  /**
   * Mock GoogleAiService for tests
   * @returns {GoogleAiServiceMock} Mock instance
   */
  static mockGoogleAiService() {
    if (!this.googleAiServiceMock) {
      this.googleAiServiceMock = new GoogleAiServiceMock();
    }

    // Mock the GoogleAiService module
    jest.doMock('../../src/services/external/GoogleAiService', () => {
      return jest.fn().mockImplementation(() => this.googleAiServiceMock);
    });

    return this.googleAiServiceMock;
  }

  /**
   * Mock OnetService for tests
   * @returns {OnetServiceMock} Mock instance
   */
  static mockOnetService() {
    if (!this.onetServiceMock) {
      this.onetServiceMock = new OnetServiceMock();
    }

    // Mock the OnetService module
    jest.doMock('../../src/services/OnetService', () => {
      return jest.fn().mockImplementation(() => this.onetServiceMock);
    });

    return this.onetServiceMock;
  }

  /**
   * Mock both GoogleAiService and OnetService
   * @returns {Object} Object containing both mock instances
   */
  static mockExternalServices() {
    return {
      googleAiService: this.mockGoogleAiService(),
      onetService: this.mockOnetService(),
    };
  }

  /**
   * Reset all mocks
   */
  static resetAllMocks() {
    if (this.googleAiServiceMock) {
      this.googleAiServiceMock.resetMocks();
    }
    if (this.onetServiceMock) {
      this.onetServiceMock.resetMocks();
    }
    jest.clearAllMocks();
  }

  /**
   * Restore all mocked modules
   */
  static restoreAllMocks() {
    this.resetAllMocks();
    jest.restoreAllMocks();
    this.googleAiServiceMock = null;
    this.onetServiceMock = null;
  }

  /**
   * Helper to mock successful job description generation
   * @param {Object} customResponse - Custom response object (optional)
   */
  static mockSuccessfulJobDescGeneration(customResponse = null) {
    const mock = this.mockGoogleAiService();

    if (customResponse) {
      mock.mockGenerateContent(customResponse);
    } else {
      // Use default successful response
      const defaultResponse = {
        candidates: [
          {
            content: {
              parts: [
                {
                  text: JSON.stringify({
                    key_responsibilities: [
                      'Lead software development projects',
                      'Mentor junior developers',
                      'Design system architecture',
                      'Ensure code quality and best practices',
                    ],
                    qualifications: [
                      "Bachelor's degree in Computer Science",
                      '5+ years of software development experience',
                      'Strong leadership skills',
                      'Experience with modern frameworks',
                    ],
                    preferred_qualifications: [
                      "Master's degree preferred",
                      'Cloud platform experience',
                      'Agile methodology experience',
                    ],
                    benefits: [
                      'Competitive salary',
                      'Health insurance',
                      'Professional development budget',
                      'Remote work options',
                    ],
                  }),
                },
              ],
            },
          },
        ],
      };
      mock.mockGenerateContent(defaultResponse);
    }

    return mock;
  }

  /**
   * Helper to mock failed external service calls
   * @param {number} status - HTTP status code
   * @param {string} message - Error message
   */
  static mockExternalServiceError(status = 503, message = 'Service temporarily unavailable') {
    const googleMock = this.mockGoogleAiService();
    const onetMock = this.mockOnetService();

    googleMock.mockError(status, message);
    onetMock.mockError(message);

    return { googleMock, onetMock };
  }

  /**
   * Helper to mock KSAO generation
   * @param {Object} customKsao - Custom KSAO object (optional)
   */
  static mockKsaoGeneration(customKsao = null) {
    const mock = this.mockGoogleAiService();

    const ksao = customKsao || {
      skills: ['JavaScript', 'React', 'Node.js', 'Database Design', 'API Development'],
      abilities: [
        'Problem Solving',
        'Critical Thinking',
        'Communication',
        'Teamwork',
        'Adaptability',
      ],
      knowledges: [
        'Software Engineering',
        'Web Technologies',
        'Database Systems',
        'Version Control',
        'Testing',
      ],
      other_characteristics: [
        'Attention to Detail',
        'Initiative',
        'Reliability',
        'Creativity',
        'Time Management',
      ],
    };

    const response = {
      candidates: [
        {
          content: {
            parts: [
              {
                text: JSON.stringify(ksao),
              },
            ],
          },
        },
      ],
    };

    mock.mockGenerateContent(response);
    return mock;
  }

  /**
   * Helper to mock O*NET occupation data
   * @param {Object} customOccupations - Custom occupations object (optional)
   */
  static mockOnetOccupations(customOccupations = null) {
    const mock = this.mockOnetService();

    const occupations = customOccupations || {
      '15-1252.00': {
        title: 'Software Developers, Applications',
        description:
          'Develop, create, and modify general computer applications software or specialized utility programs.',
      },
    };

    mock.mockGetOccupations(occupations);
    return mock;
  }
}

module.exports = MockUtils;
