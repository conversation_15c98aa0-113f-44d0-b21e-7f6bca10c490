const { describe, beforeEach, afterEach } = require('@jest/globals');
const { sequelize } = require('../../src/models');

/**
 * A helper function that wraps a Jest describe block with transaction management.
 * It starts a transaction before each test in the block and rolls it back after.
 *
 * @param {string} name - The name of the test suite (passed to `describe`).
 * @param {Function} callback - The function containing your tests (passed to `describe`).
 */
function describeWithTransaction(name, callback) {
  describe(name, () => {
    let transaction;

    // Wrap each example in a transaction
    beforeEach(async () => {
      const cls = new Map();
      Object.defineProperty(cls, 'run', {
        value: fn => {
          fn(this);
          return this;
        },
      });
      transaction = await sequelize.transaction();
      sequelize.constructor._cls = cls;
      sequelize.constructor._cls.set('transaction', transaction);
    });

    afterEach(async () => {
      await transaction.rollback();
    });

    // Now, execute the user's original callback which contains all the tests (`it` blocks)
    callback();
  });
}

module.exports = { describeWithTransaction };
