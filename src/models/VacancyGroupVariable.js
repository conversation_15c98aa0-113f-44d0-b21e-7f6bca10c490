'use strict';
const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class VacancyGroupVariable extends AppModel {
  static associate(models) {
    this.belongsTo(models.JobVacancy, {
      foreignKey: 'job_vacancy_id',
      as: 'jobVacancy',
    });
    this.belongsTo(models.JobGroupVariable, {
      foreignKey: 'job_group_variable_id',
      as: 'jobGroupVariable',
    });
    this.hasMany(models.UserVacancyGroupVariable, {
      foreignKey: 'vacancy_group_variable_id',
      as: 'userVacancyGroupVariables',
    });
  }

  static schema() {
    return {
      match_type: {
        type: DataTypes.STRING,
        validate: {
          isIn: [['filter', 'weight']],
        },
      },
    };
  }
}

module.exports = VacancyGroupVariable;
