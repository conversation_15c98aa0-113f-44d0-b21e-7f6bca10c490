'use strict';
const AppModel = require('./AppModel');

class UserBone extends AppModel {
  /**
   * Helper method for defining associations.
   * This method is not a part of Sequelize lifecycle.
   * The `models/index` file will call this method automatically.
   */
  static associate(models) {
    this.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });

    this.belongsTo(models.Bone, {
      foreignKey: 'bone_id',
      as: 'bone',
    });
  }
}

module.exports = UserBone;
