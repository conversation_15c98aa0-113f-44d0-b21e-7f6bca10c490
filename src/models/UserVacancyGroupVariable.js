'use strict';
const AppModel = require('./AppModel');

class UserVacancyGroupVariable extends AppModel {
  static associate(models) {
    this.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });
    this.belongsTo(models.VacancyGroupVariable, {
      foreignKey: 'vacancy_group_variable_id',
      as: 'vacancyGroupVariable',
    });
  }
}

module.exports = UserVacancyGroupVariable;
