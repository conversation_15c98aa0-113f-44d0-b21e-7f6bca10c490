'use strict';
const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');
const config = require('../config/config');
const bcrypt = require('bcrypt');

class User extends AppModel {
  static associate(models) {
    this.hasOne(models.UserProfile, { foreignKey: 'user_id', as: 'profile' });
    this.hasMany(models.UserPosition, { foreignKey: 'user_id', as: 'positions' });
    this.hasMany(models.UserJobVacancy, { foreignKey: 'user_id', as: 'jobVacancyRecommendations' });

    this.hasMany(models.UserJobVariable, {
      foreignKey: 'user_id',
      as: 'userJobVariables',
    });

    this.hasMany(models.UserVacancyGroupVariable, {
      foreignKey: 'user_id',
      as: 'userVacancyGroupVariables',
    });

    this.hasMany(models.UserCompetenciesProfiling, {
      foreignKey: 'user_id',
      as: 'userCompetenciesProfiling',
    });

    this.hasMany(models.UserAssessmentResult, {
      foreignKey: 'user_id',
      as: 'userAssessmentResults',
    });

    this.hasMany(models.UserBone, {
      foreignKey: 'user_id',
      as: 'userBones',
    });
  }

  static schema() {
    return {
      password: {
        type: DataTypes.VIRTUAL,
        allowNull: false,
        validate: {
          notEmpty: {
            msg: 'Password cannot be empty',
          },
        },
      },
    };
  }

  static hooks() {
    return {
      beforeValidate: async user => {
        if (user.password) {
          const hashedPassword = await bcrypt.hash(user.password, config.saltRounds);
          user.password_digest = hashedPassword;
        }
      },
    };
  }

  async verifyPassword(password) {
    return await bcrypt.compare(password, this.password_digest);
  }
}

module.exports = User;
