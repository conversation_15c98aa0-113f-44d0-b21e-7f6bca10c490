'use strict';
const AppModel = require('./AppModel');

class JobGroupVariable extends AppModel {
  static associate(models) {
    this.hasMany(models.JobVariable, {
      foreignKey: 'job_group_variable_id',
      as: 'jobVariables',
    });
    this.hasMany(models.VacancyGroupVariable, {
      foreignKey: 'job_group_variable_id',
      as: 'vacancyGroupVariables',
    });
  }
}

module.exports = JobGroupVariable;
