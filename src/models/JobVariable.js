'use strict';
const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class JobVariable extends AppModel {
  static associate(models) {
    this.belongsTo(models.JobGroupVariable, {
      foreignKey: 'job_group_variable_id',
      as: 'jobGroupVariable',
    });
    this.hasMany(models.UserJobVariable, {
      foreignKey: 'job_variable_id',
      as: 'userJobVariables',
    });
  }

  static schema() {
    return {
      variable_type: {
        type: DataTypes.STRING,
        validate: {
          isIn: [['numeric', 'categorical']],
        },
      },
    };
  }
}

module.exports = JobVariable;
