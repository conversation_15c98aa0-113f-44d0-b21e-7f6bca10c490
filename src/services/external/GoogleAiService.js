const AppService = require('../AppService');
const { GoogleGenAI } = require('@google/genai');

class GoogleAiService extends AppService {
  constructor() {
    super();
    this.genAI = new GoogleGenAI({ apiKey: this.config.geminiApiKey });
  }

  async generateContent(params) {
    try {
      const response = await this.genAI.models.generateContent(params);
      return response;
    } catch (error) {
      if (error.status === 503 || error.status === 429) {
        await new Promise(resolve => setTimeout(resolve, 10000));
        return this.generateContent(params);
      }

      throw error;
    }
  }

  async embedContent(params) {
    try {
      const response = await this.genAI.models.embedContent(params);
      return response;
    } catch (error) {
      if (error.status === 503 || error.status === 429) {
        await new Promise(resolve => setTimeout(resolve, 10000));
        return this.embedContent(params);
      }

      throw error;
    }
  }
}

module.exports = GoogleAiService;
