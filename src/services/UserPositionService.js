const AppService = require('./AppService');
const UserPositionsRepository = require('../repositories/UserPositionsRepository');

class UserPositionService extends AppService {
  constructor() {
    super();
    this.repository = new UserPositionsRepository();
  }

  /**
   * Get position history for a user (admin only)
   * @param {Object} params - Query params (page, limit, user_id, etc.)
   * @returns {Object} - Position history array and pagination info
   */
  async findAll(params = {}) {
    const { rows, pagination } = await this.repository.findAll(params);
    return { userPositions: rows, pagination };
  }
}

module.exports = UserPositionService;
