const AppService = require('./AppService');
const {
  VacancyGroupVariable,
  // JobGroupVariable,
  JobVacancy,
  JobVariable,
  sequelize,
} = require('../models');

const JobVacancyService = require('./JobVacancyService');
const VacancyGroupVariablesRepository = require('../repositories/VacancyGroupVariablesRepository');

class VacancyGroupVariableService extends AppService {
  constructor() {
    super();
    this.repository = new VacancyGroupVariablesRepository();
  }

  /**
   * Find all vacancy group variables for a job vacancy with pagination and sorting
   * @param {Object} queryParams - Query parameters including job_vacancy_id
   * @returns {Object} Vacancy group variables with pagination info
   */
  async findAll(params = {}) {
    params['sort'] = params.sort || 'order_level';
    params['sort_direction'] = params.sort_direction || 'asc';
    params.includes = ['jobVariables'];

    const baselineSql = `
      SELECT ujv.job_variable_id,
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY ujv.normalized_value) AS benchmark_score
      FROM user_job_variables ujv
      JOIN job_variables jv ON jv.id = ujv.job_variable_id
      JOIN job_group_variables jgv ON jgv.id = jv.job_group_variable_id
      JOIN vacancy_group_variables vgv ON vgv.job_group_variable_id = jgv.id
      JOIN job_vacancies ON job_vacancies.id = vgv.job_vacancy_id
      WHERE job_vacancies.id = :job_vacancy_id
        AND ujv.user_id = ANY(job_vacancies.related_user_ids)
      GROUP BY ujv.job_variable_id;
    `;

    const baselineResult = await sequelize.query(baselineSql, {
      replacements: { job_vacancy_id: params.job_vacancy_id },
      type: sequelize.QueryTypes.SELECT,
    });

    const baselineValues = {};
    baselineResult.forEach(row => {
      baselineValues[row.job_variable_id] = row.benchmark_score;
    });

    const { rows, pagination } = await this.repository.findAll(params);
    return { vacancyGroupVariables: rows, pagination, baselineValues };
  }

  /**
   * Bulk update vacancy group variables
   * @param {Array} vacancyGroupVariables - Array of vacancy group variables to update
   * @param {Object} options - Options including transaction
   * @returns {Object} Update result
   */
  async bulkUpdate(vacancyGroupVariables) {
    const useTransaction = process.env.NODE_ENV !== 'test';
    const transaction = useTransaction ? await sequelize.transaction() : null;

    try {
      const jobVacancyIds = [];
      const updatePromises = vacancyGroupVariables.map(async vgvData => {
        const { id, match_type, weight } = vgvData;

        const findOptions = transaction ? { transaction } : {};
        const existingVgv = await VacancyGroupVariable.findByPk(id, findOptions);
        this.exists(existingVgv, `Vacancy group variable with id ${id} not found`);
        jobVacancyIds.push(existingVgv.job_vacancy_id);

        const updateData = {
          match_type,
          updated_at: new Date(),
        };

        if (match_type === 'weight') {
          this.assert(
            weight !== undefined && weight !== null,
            'Weight is required for weight match type',
          );
          updateData.weight = weight;
        } else if (match_type === 'filter') {
          updateData.weight = 0;
        }

        const updateOptions = transaction ? { transaction } : {};
        await existingVgv.update(updateData, updateOptions);

        return existingVgv;
      });

      const updatedRecords = await Promise.all(updatePromises);

      this.assert(
        jobVacancyIds.every(id => id === jobVacancyIds[0]),
        'All vacancy group variables must belong to the same job vacancy',
      );
      const jobVacancy = await JobVacancy.findByPk(jobVacancyIds[0]);
      await jobVacancy.update({ status: 'calculating_match_scores' });

      if (useTransaction) {
        await transaction.commit();
      }

      this.calculateUserVacancyVariableScores(jobVacancy.id);

      return {
        updated_count: updatedRecords.length,
        updated_records: updatedRecords,
      };
    } catch (error) {
      if (useTransaction) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async calculateUserVacancyVariableScores(jobVacancyId) {
    const jobVacancyService = new JobVacancyService();
    const jobVacancy = await JobVacancy.findByPk(jobVacancyId);
    const userIds = jobVacancy.related_user_ids || [];

    const vgv = await VacancyGroupVariable.findAll({
      where: { job_vacancy_id: jobVacancyId },
    });

    await Promise.all(
      vgv.map(async vgv => {
        const vars = await JobVariable.findAll({
          where: { job_group_variable_id: vgv.job_group_variable_id },
        });

        await Promise.all(
          vars.map(async jobVar => {
            const sql = `
              WITH benchmark_scores AS (
                SELECT ujv.job_variable_id,
                  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY ujv.normalized_value) AS benchmark_score
                FROM user_job_variables ujv
                WHERE ujv.job_variable_id = :job_variable_id
                  AND ujv.user_id IN (:user_ids)
                GROUP BY ujv.job_variable_id
              ), calculation_data AS (
                SELECT u.id AS user_id,
                  ujv.normalized_value,
                  COALESCE(bs.benchmark_score, jv.normalized_baseline) AS baseline
                FROM users u
                LEFT JOIN user_job_variables ujv
                  ON ujv.user_id = u.id
                  AND ujv.job_variable_id = :job_variable_id
                LEFT JOIN job_variables jv ON jv.id = ujv.job_variable_id
                LEFT JOIN benchmark_scores bs ON bs.job_variable_id = ujv.job_variable_id
              )

              INSERT INTO user_vacancy_variable_scores (
                user_id,
                job_vacancy_id,
                job_variable_id,
                match_score,
                created_at,
                updated_at
              )

              SELECT cd.user_id,
                (:job_vacancy_id)::int AS job_vacancy_id,
                (:job_variable_id)::int AS job_variable_id,
                CASE
                  WHEN COALESCE(cd.baseline, 0) = 0 THEN NULL
                  ELSE LEAST(
                    GREATEST(
                      (cd.normalized_value::float / cd.baseline) * 100.0,
                      0
                    ),
                    100
                  )
                END AS match_score,
                NOW() AS created_at,
                NOW() AS updated_at
              FROM calculation_data cd

              ON CONFLICT (user_id, job_vacancy_id, job_variable_id)
              DO UPDATE SET match_score = EXCLUDED.match_score,
                updated_at = EXCLUDED.updated_at;
            `;

            await sequelize.query(sql, {
              replacements: {
                job_variable_id: jobVar.id,
                job_vacancy_id: jobVacancyId,
                user_ids: userIds,
              },
              type: sequelize.QueryTypes.RAW,
            });
          }),
        );
      }),
    );

    await jobVacancyService.averagingVarGroup(jobVacancyId);
    await jobVacancyService.weightingMatchRate(jobVacancyId);
    await jobVacancy.update({ status: 'active' });
  }
}

module.exports = VacancyGroupVariableService;
