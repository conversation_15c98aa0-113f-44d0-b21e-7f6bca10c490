const AppService = require('./AppService');
const BonesRepository = require('../repositories/BonesRepository');

class BoneService extends AppService {
  constructor() {
    super();
    this.repository = new BonesRepository();
  }

  /**
   * Get all bones with pagination
   * @param {Object} params - Query params (page, limit, etc.)
   * @returns {Object} - Bones array and pagination info
   */
  async findAll(params = {}) {
    params['sort'] = params.sort || 'name';
    params['sort_direction'] = params.sort_direction || 'asc';
    const { rows, pagination } = await this.repository.findAll(params);
    return { bones: rows, pagination };
  }
}

module.exports = BoneService;
