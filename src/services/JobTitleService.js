const AppService = require('./AppService');
const JobTitlesRepository = require('../repositories/JobTitlesRepository');

class JobTitleService extends AppService {
  constructor() {
    super();
    this.repository = new JobTitlesRepository();
  }

  /**
   * Get all job titles with pagination
   * @param {Object} params - Query params (page, limit, etc.)
   * @returns {Object} - Job titles array and pagination info
   */
  async findAll(params = {}) {
    const { rows, pagination } = await this.repository.findAll(params);
    return { job_titles: rows, pagination };
  }
}

module.exports = JobTitleService;
