const AppService = require('./AppService');
const JobLevelsRepository = require('../repositories/JobLevelsRepository');

class JobLevelService extends AppService {
  constructor() {
    super();
    this.repository = new JobLevelsRepository();
  }

  /**
   * Get all job levels with pagination
   * @param {Object} params - Query params (page, limit, etc.)
   * @returns {Object} - Job levels array and pagination info
   */
  async findAll(params = {}) {
    params['sort'] = params.sort || 'order_level';
    params['sort_direction'] = params.sort_direction || 'asc';
    const { rows, pagination } = await this.repository.findAll(params);
    return { job_levels: rows, pagination };
  }
}

module.exports = JobLevelService;
