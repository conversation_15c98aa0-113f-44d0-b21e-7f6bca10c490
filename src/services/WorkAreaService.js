const AppService = require('./AppService');
const WorkAreasRepository = require('../repositories/WorkAreasRepository');

class WorkAreaService extends AppService {
  constructor() {
    super();
    this.repository = new WorkAreasRepository();
  }

  /**
   * Get all work areas with pagination
   * @param {Object} params - Query params (page, limit, etc.)
   * @returns {Object} - Work area array and pagination info
   */
  async findAll(params = {}) {
    params['sort'] = params.sort || 'name';
    params['sort_direction'] = params.sort_direction || 'asc';
    const { rows, pagination } = await this.repository.findAll(params);
    return { workAreas: rows, pagination };
  }
}

module.exports = WorkAreaService;
