const AppService = require('../AppService');
const { InternalJobProfileData, UserAssessmentResult, LlmMetadata } = require('../../models');

class GenerateJobDescService extends AppService {
  constructor({ onetService, googleAiService, qdrantClient }) {
    super();
    this.onetService = onetService;
    this.googleAiService = googleAiService;
    this.qdrantClient = qdrantClient;
  }

  /**
   * Generate job description based on job title name
   * @param {string} name - Job title name
   * @param {Array} topUserIds - Array of top user ids
   * @returns {Promise<Object>} Job description object
   */
  async generateJobDesc(name = '', topUserIds = [], jobLevelName = '', roleSummary = '') {
    const relatedJobTitles = await this.getRelatedJobTitles(name);
    const allJobTitles = [name, ...relatedJobTitles];

    const vectorizedJobTitles = await this.generateVectorTitles(allJobTitles);

    const [onetResults, internalResults] = await Promise.all([
      this.qdrantSearchOnet(vectorizedJobTitles),
      this.qdrantSearchInternal(vectorizedJobTitles),
    ]);

    const searchResults = {
      onetResults,
      internalResults,
    };

    const onetCodes = searchResults.onetResults.map(item => item.onetsoc_code);

    const contextData = await this.processContextData(searchResults, topUserIds);
    const jobDescription = await this.generateJobDescriptionWithAI(
      name,
      jobLevelName,
      roleSummary,
      contextData,
    );

    return {
      jobTitle: name,
      jobDescription,
      onetsocCodes: onetCodes,
    };
  }

  /**
   * Get 3 related job titles using Gemini API
   * @param {string} jobTitle - Original job title
   * @returns {Array} Array of 3 related job titles
   */
  async getRelatedJobTitles(jobTitle) {
    const systemPrompt = `
        You are an expert HR professional. Given a job title, 
        provide 3 closely related job titles that share similar skills, responsibilities, or career paths. 
        Return only a JSON array of strings with exactly 3 job titles.
    `;

    const userPrompt = `Job Title: ${jobTitle}\n\nProvide 3 closely related job titles:`;

    try {
      const aiParams = {
        model: 'gemini-2.5-flash',
        contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
        config: {
          temperature: 0,
          responseMimeType: 'application/json',
          systemInstruction: [{ text: systemPrompt }],
        },
      };

      const response = await this.googleAiService.generateContent(aiParams);

      LlmMetadata.create({
        request: aiParams,
        responses: response,
        action_type: 'get_related_job_titles',
      });

      const relatedTitles = JSON.parse(response.candidates[0].content.parts[0].text);
      return relatedTitles.slice(0, 3);
    } catch (error) {
      console.error('Error getting related job titles:', error);
      // Fallback to mock data
      return ['Senior Software Engineer', 'Full Stack Developer'];
    }
  }

  /**
   * Generate vector embeddings for job titles
   * @param {Array} jobTitles - Array of job titles
   * @returns {Array} Array of objects with jobTitle and vector
   */
  async generateVectorTitles(jobTitles) {
    const responses = await this.googleAiService.embedContent({
      model: 'gemini-embedding-001',
      contents: jobTitles,
    });

    const results = responses.embeddings.map((embedding, index) => ({
      jobTitle: jobTitles[index],
      vector: embedding.values,
    }));

    return results;
  }

  /**
   * Search Qdrant for ONET collection data
   * @param {Array} jobTitles - Job title to search
   * @returns {Array} Array of ONET results with onetsoc_code
   */
  async qdrantSearchOnet(jobTitles) {
    const collectionName = 'onet_job_title';

    const searches = jobTitles.map(({ _, vector }) => ({
      vector,
      limit: 3,
    }));

    const searchResult = await this.qdrantClient.searchBatch(collectionName, {
      searches,
    });

    const searchOnetResults = searchResult.flatMap(item => item.flatMap(result => result.id));

    const onetData = await this.qdrantClient.retrieve(collectionName, {
      ids: searchOnetResults,
    });

    const retriveData = onetData.map(item => ({
      onetsoc_code: item.payload.onetsoc_code,
      title: item.payload.job_title,
    }));

    return retriveData;
  }

  /**
   * Search Qdrant for internal collection data
   * @param {Array} jobTitles - Job title to search
   * @returns {Array} Array of internal results with table_internal_id
   */
  async qdrantSearchInternal(jobTitles) {
    const collectionName = 'internal_job_title';

    const searches = jobTitles.map(({ _, vector }) => ({
      vector,
      limit: 3,
    }));

    const searchResult = await this.qdrantClient.searchBatch(collectionName, {
      searches,
    });

    const qdrant_ids = searchResult.flatMap(item => item.flatMap(result => result.id));

    const internalData = await this.qdrantClient.retrieve(collectionName, {
      ids: qdrant_ids,
    });

    const retriveData = internalData.map(item => ({
      table_internal_id: item.payload.table_internal_id,
      title: item.payload.job_title,
    }));

    return retriveData;
  }

  /**
   * Process context data from ONET search results
   * @param {Array} onetResults - Array of ONET search results
   * @returns {Object} Processed ONET context data
   */
  async onetContextData(onetResults) {
    const onetsocCodes = onetResults.map(result => result.onetsoc_code);

    if (onetsocCodes.length === 0) {
      return {
        onetData: 'No onet data available',
        source: 'onet',
      };
    }

    const resultOccupations = await this.onetService.getOccupations(onetsocCodes);
    const resultTasks = await this.onetService.getTasks(onetsocCodes);

    const occupations = Object.values(resultOccupations)
      .map(result => `Job Title: ${result.title}\nJob Description: ${result.description}\n`)
      .join('\n\n');

    const tasks = resultTasks
      .map(
        result =>
          `Task: ${result.task}\nTask Type: ${result.task_type}\n Importance: ${result.importance}`,
      )
      .join('\n\n');

    return {
      occupations,
      tasks,
      source: 'onet',
    };
  }

  /**
   * Process context data from internal search results
   * @param {Array} internalResults - Array of internal search results
   * @returns {Object} Processed internal context data
   */
  async internalContextData(internalResults) {
    const internalIds = internalResults.map(result => result.table_internal_id);

    if (internalIds.length === 0) {
      return {
        internalData: 'No internal data available',
        source: 'internal',
      };
    }

    const results = await InternalJobProfileData.findAll({ where: { id: internalIds } });

    const internalData = results
      .map(
        result =>
          `Job Title: ${result.position_name}\nJob Description: ${result.main_responsibilities}\n`,
      )
      .join('\n\n');

    return {
      internalData,
      source: 'internal',
    };
  }

  /**
   * Process context data from user assessment results
   * @param {Array} userIds - Array of user IDs
   * @returns {Object} Processed user assessment data
   */
  async userAssessmentData(userIds) {
    if (!userIds || userIds.length === 0) {
      return {
        userAssessmentData: 'No user assessment data available',
        source: 'user_assessment',
      };
    }

    const userAssessmentResults = await UserAssessmentResult.findAll({
      where: {
        user_id: userIds,
      },
    });

    // group assesment data by user_ids and assessment
    const groupedAssessmentData = userAssessmentResults.reduce((acc, cur) => {
      if (!acc[cur.user_id]) {
        acc[cur.user_id] = {};
      }
      acc[cur.user_id][cur.assessment] = cur;
      return acc;
    }, {});

    // format user assessment data
    const userAssessmentData = Object.entries(groupedAssessmentData).map(
      ([userId, data]) =>
        `
      User ID: ${userId}
      ${Object.entries(data)
        .map(
          ([assessment, result]) =>
            `
        Assessment: ${assessment}
        Aspect Name: ${result.aspect_name}
        Value Type: ${result.value_type}
        Value: ${result.value}
        `,
        )
        .join('\n')}
      `,
    );

    return {
      userAssessmentData,
      source: 'user_assessment',
    };
  }

  /**
   * Process all context data from search results
   * @param {Array} searchResults - Combined search results
   * @param {Array} topUserIds - Array of top user IDs
   * @returns {Object} Processed context data
   */
  async processContextData(searchResults, topUserIds) {
    const [onetContext, internalContext, userAssessmentContext] = await Promise.all([
      this.onetContextData(searchResults.onetResults),
      this.internalContextData(searchResults.internalResults),
      this.userAssessmentData(topUserIds),
    ]);

    return {
      onetContext,
      internalContext,
      userAssessmentContext,
    };
  }

  /**
   * Generate job description using AI with context data
   * @param {string} jobTitle - Original job title
   * @param {Array} contextData - Processed context data
   * @returns {Object} Generated job description
   */
  async generateJobDescriptionWithAI(jobTitle, jobLevelName, roleSummary, contextData) {
    try {
      const [systemPrompt, userPrompt] = await Promise.all([
        this.getSystemPrompt(),
        this.getUserPrompt(jobTitle, jobLevelName, roleSummary, contextData),
      ]);

      const aiParams = {
        model: 'gemini-2.5-pro',
        contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
        config: {
          temperature: 0.4,
          systemInstruction: [{ text: systemPrompt }],
        },
      };

      const response = await this.googleAiService.generateContent(aiParams);

      LlmMetadata.create({
        request: aiParams,
        responses: response,
        action_type: 'generate_job_description',
      });

      const plainResponse = response.candidates[0].content.parts[0].text;
      const structuredResponse = await this.googleAiService.generateContent({
        model: 'gemini-2.5-flash',
        contents: [
          {
            role: 'user',
            parts: [
              {
                text: `
            Here is the job description:
            ${plainResponse}
          `,
              },
            ],
          },
        ],
        config: {
          temperature: 0,
          responseMimeType: 'application/json',
          systemInstruction: [
            {
              text: `
            You are a JSON formatter.
            You receive a job description in unstructured format.
            Format them and return in JSON format with the following structure:
            {
              "key_responsibilities": [
                "string"
              ],
              "qualifications": [
                "string"
              ],
              "competencies": [
                "string"
              ],
              "success_metrics": [
                "string"
              ]
            }
            `,
            },
          ],
        },
      });

      const jobDescription = JSON.parse(structuredResponse.candidates[0].content.parts[0].text);
      return jobDescription;
    } catch (error) {
      console.error('Error generating job description:', error);
      return {
        key_responsibilities: [],
        qualifications: [],
        competencies: [],
        success_metrics: [],
      };
    }
  }

  /**
   * Get system prompt for job description generation
   * @param {string} jobTitle - Job title
   * @param {Array} contextData - Context data from searches
   * @returns {string} System prompt
   */
  async getSystemPrompt() {
    return Promise.resolve(`
      You are an expert HR consultant tasked with creating a comprehensive job description for Paragon Technology & Innovation, a leading Indonesian beauty company. Use the following company context and framework to generate a detailed job description.

      # **COMPANY CONTEXT**

      ## **Main Business Model**

      Paragon conducts an end-to-end process, from ideation to production and distribution, with supporting roles in between. Their business model includes:

      * **Vertical Integration**: Complete control from R&D to retail  
      * **Multi-brand Portfolio Strategy**: Different brands targeting various market segments  
        * Wardah - halal beauty for Muslim women  
        * Emina - Youth-focused cosmetics  
        * Make Over - professional makeup  
        * Kahf - men's grooming products  
        * Biodef - halal natural antiseptic body wash  
      * **Innovation-driven Product Development**: Strong R&D focus with continuous product innovation  
      * **Halal Certification Focus**: GMP (Good Manufacturing Practice) and HAS (Halal Assurance System) certification

      ## **Core Values**

      1. Faith in God - Paragonian believes in the existence and power of God  
      2. Care - Paragonian upholds the value of togetherness and compassion  
      3. Humility - Paragonian realizes that everyone has strengths and weaknesses  
      4. Grit - Paragonian lives a life with great joy and willpower  
      5. Innovation - Paragonian always develops new and better things to meet and exceed customer expectations

      ## **Company Motto & Philosophy**

      "Innovation For The Greater Good" ([#InnovationForTheGreaterGood](https://mattermost.rakamin.com/rakamin/channels/InnovationForTheGreaterGood))

      ## **Culture Characteristics**

      * **Innovation Culture**: Strong emphasis on continuous innovation and improvement  
      * **Employee Development**: "The integrity, agility, and persistency of our people preserves Paragon's position as market leader"  
      * **Youth Empowerment**: Empowering young employees to pitch ideas and lead projects  
      * **Social Responsibility**: CSR programs in 4 pillars: Education, Health, Environment, and Women Empowerment  
      * **Educational Focus**: Strong belief that education can advance the nation

      # **FRAMEWORK DEFINITIONS**

      * **PEMR Framework**: Plan, Execute, Monitor, Report - verbs that should be integrated into responsibilities based on job level hierarchy   
      * **KSAO Framework**: Knowledge [K], Skills [S], Abilities [A], Others [O] - mark each responsibility with appropriate KSAO category   
      * **Job Levels**: Officer → Executive → Group Head → Head → Director → Executive Committee (from lowest to highest)

      # **STAKEHOLDER MAPPING CATEGORIES**

      * **Superiors**: Direct supervisor, senior management, board members  
      * **Coworkers**: Peers, team members, project collaborators  
      * **Subordinates**: Direct reports, team members under supervision  
      * **Interdepartmental**: Other departments, cross-functional teams  
      * **External Parties**: Customers, suppliers, regulatory bodies, partners, media

      # **INPUT PARAMETERS TO PROCESS**

      * **Job Role**: [SPECIFIC ROLE NAME]   
      * **Job Level**: [SPECIFIC LEVEL FROM HIERARCHY]   
      * **Role Summary**: [1-2 SENTENCES DESCRIBING THE ROLE'S PRIMARY OUTCOME/PURPOSE]

      # **OUTPUT REQUIREMENTS**

      Generate a job description with exactly these 4 components:

      ## **1. KEY RESPONSIBILITIES (8-10 bullet points)**

      * Use a variety of action verbs that correspond to each category of PEMR verbs appropriate to the job level  
      * Each responsibility must be marked with [K], [S], [A], or [O] at the end  
      * Include stakeholder mapping in italics after each responsibility: *(stakeholder type)*  
      * Ensure responsibilities reflect Paragon's business model, values, and industry context  
      * Cover various stakeholder categories throughout the list  
      * Structure: **[PEMR Mapping Verb]** [detailed responsibility description] [KSAO] *(stakeholder)*

      ## **2. QUALIFICATIONS (5-6 bullet points)**

      * Education requirements appropriate to level  
      * Years of experience matching job level progression  
      * Industry-specific knowledge (beauty, FMCG, halal certification)  
      * Regulatory knowledge (Indonesian labor laws, GMP, HAS)  
      * Technical skills and certifications relevant to role and level  
      * International experience for senior levels

      ## **3. SKILLS AND COMPETENCIES (8 skills, 1-2 words each)**

      * Balance technical and soft skills  
      * Reflect increasing strategic complexity with job level  
      * Include innovation, leadership, and analytical capabilities  
      * Consider cultural intelligence and digital transformation needs  
      * Align with Paragon's emphasis on agility, integrity, and persistency

      ## **4. SUCCESS METRICS (5 bullet points)**

      * Quantifiable outcomes with specific percentages/targets  
      * Mix of operational efficiency and strategic impact measures  
      * Escalate ambition level with job hierarchy  
      * Include compliance, employee satisfaction, and business impact metrics  
      * Reflect Paragon's focus on innovation, market leadership, and employee development

      # **QUALITY STANDARDS**

      * Ensure each responsibility covers different stakeholder relationships  
      * Maintain consistency with Paragon's halal focus and multi-brand strategy  
      * Reflect appropriate level of strategic thinking for the job level  
      * Include references to Paragon's core values and innovation culture where relevant  
      * Balance between functional expertise and business acumen requirements

      # **EXAMPLE STRUCTURE REFERENCE**

      Follow this format exactly:

      **Role Summary:** [Concise 1-2 sentence summary]

      ## **1. KEY RESPONSIBILITIES**

      * **Plan/Execute/Monitor/Report** [specific task] [K/S/A/O] *(stakeholder type)* [Repeat for 8-10 responsibilities]

      ## **2. QUALIFICATIONS**

      [5-6 qualification bullets]

      ## **3. SKILLS AND COMPETENCIES**

      [8 skills in 1-2 words each]

      ## **4. SUCCESS METRICS**

      [5 quantifiable success measures]
    `);
  }

  /**
   * Generate user prompt with job title and context data
   * @param {string} jobTitle - Job title
   * @returns {string} User prompt
   */
  async getUserPrompt(jobTitle, jobLevelName, roleSummary, contextData) {
    let userPrompt = `
      Job Role: ${jobTitle}
      Job Level: ${jobLevelName}
      Role Summary: ${roleSummary}
    `;

    if (this.config.jobDescWithOnet) {
      userPrompt += `
        **Additional External Context Data:**

        **1. Related Internal Company Job Profiles:**
        ${Array.isArray(contextData) ? contextData.map(item => item.internalContext).join('\n') : contextData.internalContext}

        **2. Related O*Net Job Descriptions:**
        ${Array.isArray(contextData) ? contextData.map(item => item.onetContext).join('\n') : contextData.onetContext}

        **3. Related O*Net Tasks:**
        Task contains: Task Description, Task Type, and Importance 1-100 (higher importance means more important)
        ${Array.isArray(contextData) ? contextData.map(item => item.tasks || '').join('\n') : contextData.tasks || ''}

        **4. Top Related Employee Data**
        ${Array.isArray(contextData) ? contextData.map(item => item.userAssessmentContext).join('\n') : contextData.userAssessmentContext}
      `;
    }

    return Promise.resolve(userPrompt);
  }
}

module.exports = GenerateJobDescService;
