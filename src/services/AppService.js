const config = require('../config/config');
const InvalidError = require('../errors/InvalidError');
const NotFoundError = require('../errors/NotFoundError');

class AppService {
  constructor() {
    this.config = config;
  }

  assert(condition, message) {
    if (!condition) {
      throw new InvalidError(message);
    }
  }

  exists(object, message) {
    if (!object) {
      throw new NotFoundError(message);
    }
  }
}

module.exports = AppService;
