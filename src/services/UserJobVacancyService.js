const AppService = require('./AppService');
const UserJobVacanciesRepository = require('../repositories/UserJobVacanciesRepository');
const { JobVacancy } = require('../models');

class UserJobVacanciesService extends AppService {
  constructor() {
    super();
    this.repository = new UserJobVacanciesRepository();
  }

  /**
   * Get all user job vacancy recommendations with pagination and filtering
   * @param {Object} params - Query params (page, limit, job_vacancy_id, etc.)
   * @returns {Object} - UserJobVacancy records array and pagination info
   */
  async findAll(params = {}) {
    const jobVacancy = await JobVacancy.findByPk(params.job_vacancy_id);
    this.exists(jobVacancy, 'Job vacancy not found');

    params['sort'] = params.sort || 'match_rate';
    params['sort_direction'] = params.sort_direction || 'desc';
    params['includes'] = ['user'];
    const { rows, pagination } = await this.repository.findAll(params);
    const ujvIds = rows.map(row => row.id);

    let groupScores = [];
    if (ujvIds.length > 0) {
      const sqlGroupScores = `
        WITH grouped_variables AS (
          SELECT ujv.id AS user_job_vacancy_id,
            jgv.id AS job_group_variable_id,
            jsonb_agg(
              jsonb_build_object(
                'id', jv.id,
                'name', jv.name,
                'raw_value', ujvar.raw_value
              )
              ORDER BY jv.name
            ) AS variables
          FROM user_job_vacancies AS ujv
          JOIN vacancy_group_variables AS vgv ON vgv.job_vacancy_id = ujv.job_vacancy_id
          JOIN job_group_variables AS jgv ON jgv.id = vgv.job_group_variable_id
          JOIN job_variables AS jv ON jv.job_group_variable_id = jgv.id
          LEFT JOIN user_job_variables AS ujvar
            ON ujvar.job_variable_id = jv.id
            AND ujvar.user_id = ujv.user_id
          WHERE ujv.id IN (:ujvIds)
          GROUP BY ujv.id, jgv.id
        )

        SELECT ujv.id,
          jsonb_agg(
            jsonb_build_object(
              'id', jgv.id,
              'name', jgv.name,
              'avg_match_score', uvgv.average_match_score,
              'variables', COALESCE(gv.variables, '[]'::jsonb)
            ) ORDER BY jgv.name
          ) AS group_scores
        FROM user_job_vacancies AS ujv
        JOIN vacancy_group_variables AS vgv ON vgv.job_vacancy_id = ujv.job_vacancy_id
        JOIN job_group_variables AS jgv ON jgv.id = vgv.job_group_variable_id
        LEFT JOIN user_vacancy_group_variables AS uvgv
          ON uvgv.vacancy_group_variable_id = vgv.id
          AND uvgv.user_id = ujv.user_id
        LEFT JOIN grouped_variables AS gv
          ON gv.user_job_vacancy_id = ujv.id
          AND gv.job_group_variable_id = jgv.id
        WHERE ujv.id IN (:ujvIds)
        GROUP BY ujv.id;
      `;

      groupScores = await this.repository.model.sequelize.query(sqlGroupScores, {
        replacements: { ujvIds },
        type: this.repository.model.sequelize.QueryTypes.SELECT,
      });
    }

    const groupScoresById = groupScores.reduce((acc, row) => {
      acc[row.id] = row.group_scores;
      return acc;
    }, {});

    return {
      userJobVacancies: rows,
      pagination,
      groupScoresById,
    };
  }

  /**
   * Find a specific user job vacancy recommendation by ID
   * @param {number} id - UserJobVacancy ID
   * @returns {Object} - UserJobVacancy with user and profile data
   */
  async findById(id) {
    // Validate ID is a valid integer
    const parsedId = parseInt(id, 10);
    this.assert(!isNaN(parsedId) && parsedId > 0, 'Invalid ID parameter');

    const userJobVacancy = await this.repository.model.findByPk(parsedId, {
      include: [
        {
          model: this.repository.model.sequelize.models.User,
          as: 'user',
          attributes: ['id', 'name'],
          include: [
            {
              model: this.repository.model.sequelize.models.UserProfile,
              as: 'profile',
              attributes: ['current_position'],
            },
          ],
        },
      ],
    });

    this.exists(userJobVacancy, 'User job vacancy recommendation not found');
    return userJobVacancy;
  }
}

module.exports = UserJobVacanciesService;
