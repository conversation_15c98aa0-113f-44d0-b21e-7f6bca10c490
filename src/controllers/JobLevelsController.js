const ApiController = require('./ApiController');
const JobLevelService = require('../services/JobLevelService');
const JobLevelsIndexInput = require('../inputs/JobLevelsIndexInput');
const JobLevelOutput = require('../outputs/JobLevelOutput');

class JobLevelsController extends ApiController {
  constructor() {
    super();
    this.service = new JobLevelService();
  }

  /**
   * Get all job levels (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    const input = new JobLevelsIndexInput(req.query);
    input.validate();
    const result = await this.service.findAll(input.output());

    const output = new JobLevelOutput(result.job_levels, {
      pagination: result.pagination,
    });

    output.renderJsonArray(res);
  });
}

module.exports = new JobLevelsController();
