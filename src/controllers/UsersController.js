const ApiController = require('./ApiController');
const UserService = require('../services/UserService');
const UsersIndexInput = require('../inputs/UsersIndexInput');
const UserOutput = require('../outputs/UserOutput');

class UsersController extends ApiController {
  constructor() {
    super();
    this.service = new UserService();
  }

  /**
   * Get all users (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    const input = new UsersIndexInput(req.query);
    input.validate();

    const result = await this.service.findAll(input.output());
    const output = new UserOutput(result.users, { pagination: result.pagination });

    output.renderJsonArray(res);
  });

  /**
   * Get a single user by ID (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  show = this.createMethod(async (req, res) => {
    const { id } = req.params;
    const user = await this.service.findById(id);

    const output = new UserOutput(user, { formatMethod: 'showFormat' });
    output.renderJson(res);
  });
}

module.exports = new UsersController();
