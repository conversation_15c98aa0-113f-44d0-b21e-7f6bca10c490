const ApiController = require('./ApiController');
const VacancyGroupVariableService = require('../services/VacancyGroupVariableService');
const VacancyGroupVariablesIndexInput = require('../inputs/VacancyGroupVariablesIndexInput');
const VacancyGroupVariablesBulkUpdateInput = require('../inputs/VacancyGroupVariablesBulkUpdateInput');
const VacancyGroupVariableOutput = require('../outputs/VacancyGroupVariableOutput');

class VacancyGroupVariablesController extends ApiController {
  constructor() {
    super();
    this.service = new VacancyGroupVariableService();
  }

  /**
   * GET /api/v1/vacancy_group_variables
   * Get all vacancy group variables for a specific job vacancy
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    const input = new VacancyGroupVariablesIndexInput(req.query);
    input.validate();

    const result = await this.service.findAll(input.output());
    const output = new VacancyGroupVariableOutput(result.vacancyGroupVariables, {
      pagination: result.pagination,
      baselineValues: result.baselineValues,
    });

    output.renderJsonArray(res);
  });

  /**
   * PATCH /api/v1/vacancy_group_variables/bulk_update
   * Bulk update vacancy group variables
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  bulkUpdate = this.createMethod(async (req, res) => {
    const input = new VacancyGroupVariablesBulkUpdateInput(req.body);
    input.validate();

    await this.service.bulkUpdate(input.output().vacancy_group_variables);
    res.status(200).json({ data: {} });
  });
}

module.exports = VacancyGroupVariablesController;
