const ApiController = require('./ApiController');
const UserPositionOutput = require('../outputs/UserPositionOutput');
const UserPositionService = require('../services/UserPositionService');
const UserPositionsIndexInput = require('../inputs/UserPositionsIndexInput');

class UserPositionsController extends ApiController {
  constructor() {
    super();
    this.service = new UserPositionService();
  }

  /**
   * Get position history for a user (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    const input = new UserPositionsIndexInput(req.query);
    input.validate();

    const result = await this.service.findAll(input.output());
    const output = new UserPositionOutput(result.userPositions, {
      pagination: result.pagination,
    });

    output.renderJsonArray(res);
  });
}

module.exports = new UserPositionsController();
