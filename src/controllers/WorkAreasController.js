const ApiController = require('./ApiController');
const WorkAreaService = require('../services/WorkAreaService');
const WorkAreasIndexInput = require('../inputs/WorkAreasIndexInput');
const WorkAreaOutput = require('../outputs/WorkAreaOutput');

class WorkAreasController extends ApiController {
  constructor() {
    super();
    this.service = new WorkAreaService();
  }

  /**
   * Get all work areas (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    const input = new WorkAreasIndexInput(req.query);
    input.validate();

    const result = await this.service.findAll(input.output());
    const output = new WorkAreaOutput(result.workAreas, {
      pagination: result.pagination,
    });

    output.renderJsonArray(res);
  });
}

module.exports = new WorkAreasController();
