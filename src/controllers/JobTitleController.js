const ApiController = require('./ApiController');
const JobTitleService = require('../services/JobTitleService');
const JobTitlesIndexInput = require('../inputs/JobTitlesIndexInput');
const JobTitleOutput = require('../outputs/JobTitleOutput');

class JobTitlesController extends ApiController {
  constructor() {
    super();
    this.service = new JobTitleService();
  }

  /**
   * Get all job titles (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    const input = new JobTitlesIndexInput(req.query);
    input.validate();
    const result = await this.service.findAll(input.output());

    const output = new JobTitleOutput(result.job_titles, {
      pagination: result.pagination,
    });

    output.renderJsonArray(res);
  });
}

module.exports = new JobTitlesController();
