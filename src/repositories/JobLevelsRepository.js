const { Op } = require('sequelize');
const BaseRepository = require('./BaseRepository');
const { JobLevel } = require('../models');

class JobLevelsRepository extends BaseRepository {
  constructor() {
    super(JobLevel);
  }

  /**
   * Filter job levels by search term (searches in name)
   * Usage: /api/v1/job_levels?search=developer
   * @param {string} search - Search term
   * @returns {Object} Where condition for search filter
   */
  filterBySearch(search) {
    if (!search) return null;

    return {
      name: {
        [Op.iLike]: `%${search}%`,
      },
    };
  }
}

module.exports = JobLevelsRepository;
