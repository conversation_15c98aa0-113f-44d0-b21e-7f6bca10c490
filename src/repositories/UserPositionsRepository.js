const BaseRepository = require('./BaseRepository');
const { UserPosition } = require('../models');

class UserPositionsRepository extends BaseRepository {
  constructor() {
    super(UserPosition);
  }

  /**
   * Filter by user ID. This method is automatically discovered
   * by BaseRepository when a 'user_id' query param is present.
   * @param {string|number} userId - The ID of the user.
   * @returns {Object|null} Sequelize where clause.
   */
  filterByUserId(userId) {
    const id = parseInt(userId, 10);
    if (!id || isNaN(id)) return null;
    return { user_id: id };
  }
}

module.exports = UserPositionsRepository;
