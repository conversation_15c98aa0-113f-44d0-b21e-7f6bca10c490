const { Op } = require('sequelize');
const BaseRepository = require('./BaseRepository');
const { User } = require('../models');

class UsersRepository extends BaseRepository {
  constructor() {
    super(User);
  }

  /**
   * Filter users by role
   * Usage: /api/v1/users?role=admin
   * @param {string} role - User role to filter by
   * @returns {Object} Where condition for role filter
   */
  filterByRole(role) {
    if (!role) return null;

    return {
      role: role.toLowerCase(),
    };
  }

  /**
   * Filter users by name (partial match)
   * Usage: /api/v1/users?name=john
   * @param {string} name - Name to search for
   * @returns {Object} Where condition for name filter
   */
  filterByName(name) {
    if (!name) return null;

    return {
      name: {
        [Op.iLike]: `%${name}%`,
      },
    };
  }

  /**
   * Filter users by email (partial match)
   * Usage: /api/v1/users?email=example.com
   * @param {string} email - Email to search for
   * @returns {Object} Where condition for email filter
   */
  filterByEmail(email) {
    if (!email) return null;

    return {
      email: {
        [Op.iLike]: `%${email}%`,
      },
    };
  }

  /**
   * Filter users by search term (searches both name and email)
   * Usage: /api/v1/users?search=john
   * @param {string} search - Search term
   * @returns {Object} Where condition for search filter
   */
  filterBySearch(search) {
    if (!search) return null;

    return {
      [Op.or]: [
        {
          name: {
            [Op.iLike]: `%${search}%`,
          },
        },
        {
          email: {
            [Op.iLike]: `%${search}%`,
          },
        },
      ],
    };
  }

  /**
   * Filter users created after a specific date
   * Usage: /api/v1/users?created_after=2024-01-01
   * @param {string} date - Date string (YYYY-MM-DD)
   * @returns {Object} Where condition for date filter
   */
  filterByCreatedAfter(date) {
    if (!date) return null;

    const parsedDate = new Date(date);
    if (isNaN(parsedDate.getTime())) return null;

    return {
      created_at: {
        [Op.gte]: parsedDate,
      },
    };
  }

  /**
   * Filter users created before a specific date
   * Usage: /api/v1/users?created_before=2024-12-31
   * @param {string} date - Date string (YYYY-MM-DD)
   * @returns {Object} Where condition for date filter
   */
  filterByCreatedBefore(date) {
    if (!date) return null;

    const parsedDate = new Date(date);
    if (isNaN(parsedDate.getTime())) return null;

    return {
      created_at: {
        [Op.lte]: parsedDate,
      },
    };
  }
}

module.exports = UsersRepository;
