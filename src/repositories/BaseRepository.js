/**
 * BaseRepository - Base repository class for read-only operations
 * Provides common functionality for sorting, pagination, and filtering
 * Child classes must implement filterBy* methods for filtering
 */
class BaseRepository {
  /**
   * Constructor
   * @param {Object} model - Sequelize model instance
   */
  constructor(model) {
    this.model = model;
  }

  /**
   * Find all records with pagination, sorting, and filtering
   * @param {Object} queryParams - Query parameters from request
   * @returns {Object} Records with pagination info
   */
  async findAll(queryParams = {}) {
    const {
      page = 1,
      limit = 10,
      sort,
      sort_direction,
      without_count,
      includes,
      ...filterParams
    } = queryParams;

    let count = null;
    let rows = null;

    let validPage = parseInt(page);
    if (validPage < 1) validPage = 1;

    let validLimit = parseInt(limit);
    if (validLimit < 1) validLimit = 1;
    if (validLimit > 100) validLimit = 100;

    // Build query options
    const queryOptions = {
      where: this.buildWhereClause(filterParams),
      limit: validLimit,
      offset: (validPage - 1) * validLimit,
      order: this.buildOrderClause(sort, sort_direction),
      include: this.buildIncludeClause(includes),
    };

    // Execute query
    if (without_count) {
      rows = await this.model.findAll(queryOptions);
    } else {
      const result = await this.model.findAndCountAll(queryOptions);
      count = result.count;
      rows = result.rows;
    }

    return {
      rows,
      pagination: {
        page: validPage,
        limit: validLimit,
        total: count,
      },
    };
  }

  /**
   * Find a single record by query parameters
   * @param {Object} queryParams - Query parameters
   * @returns {Object|null} Found record or null
   */
  async findOne(queryParams = {}) {
    queryParams.without_count = true;
    return (await this.findAll(queryParams)).rows[0];
  }

  /**
   * Build where clause combining filters
   * @param {Object} filterParams - Filter parameters
   * @returns {Object} Sequelize where conditions
   */
  buildWhereClause(filterParams) {
    const whereClause = {};

    // Apply dynamic filters based on filterBy methods
    Object.keys(filterParams).forEach(param => {
      const camelizedParam = param.replace(/_([a-z])/g, g => g[1].toUpperCase());
      const filterMethodName = `filterBy${this.capitalize(camelizedParam)}`;

      if (typeof this[filterMethodName] === 'function') {
        const filterCondition = this[filterMethodName](filterParams[param]);
        if (filterCondition) {
          Object.assign(whereClause, filterCondition);
        }
      }
    });

    return whereClause;
  }

  /**
   * Build include clause for associations
   * @param {Array} includes - Array of string
   * @returns {Array} Sequelize include array
   */
  buildIncludeClause(includes) {
    const includeClause = [];

    if (includes) {
      includes.forEach(include => {
        const includeMethodName = `include${this.capitalize(include)}`;
        if (typeof this[includeMethodName] === 'function') {
          includeClause.push(this[includeMethodName]());
        }
      });
    }

    return includeClause;
  }

  /**
   * Build order clause for sorting
   * @param {string} sort - Sort column
   * @param {string} sortDirection - Sort direction (asc/desc)
   * @returns {Array} Sequelize order array
   */
  buildOrderClause(sort, sortDirection) {
    // Default sorting
    let orderBy = [['created_at', 'DESC']];

    // Apply custom sorting if provided
    if (sort) {
      const direction = sortDirection && sortDirection.toLowerCase() === 'desc' ? 'DESC' : 'ASC';

      // Validate sort column exists in model
      if (this.isValidSortColumn(sort)) {
        orderBy = [[sort, direction]];
      }
    }

    return orderBy;
  }

  /**
   * Check if sort column is valid for the model
   * @param {string} column - Column name to validate
   * @returns {boolean} True if column is valid
   */
  isValidSortColumn(column) {
    const modelAttributes = Object.keys(this.model.rawAttributes);
    return modelAttributes.includes(column);
  }

  /**
   * Capitalize first letter of string
   * @param {string} str - String to capitalize
   * @returns {string} Capitalized string
   */
  capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * Count records with filters
   * @param {Object} filterParams - Filter parameters
   * @returns {number} Count of records
   */
  async count(filterParams = {}) {
    return await this.model.count({
      where: this.buildWhereClause(filterParams),
    });
  }

  /**
   * Check if record exists by ID
   * @param {number} id - Record ID
   * @returns {boolean} True if record exists
   */
  async exists(id) {
    const count = await this.model.count({
      where: {
        id,
      },
    });
    return count > 0;
  }
}

module.exports = BaseRepository;
