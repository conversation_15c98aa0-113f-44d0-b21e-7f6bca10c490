const { Op } = require('sequelize');
const BaseRepository = require('./BaseRepository');
const { UserJobVacancy } = require('../models');

class UserJobVacanciesRepository extends BaseRepository {
  constructor() {
    super(UserJobVacancy);
  }

  includeUser() {
    return {
      association: 'user',
      attributes: ['id', 'name'],
    };
  }

  /**
   * Custom filter for the 'job_vacancy_id' query parameter.
   * This method will be automatically called by the BaseRepository.
   * @param {string|number} id - Job vacancy ID to filter by
   * @returns {Object|null} Where condition for job_vacancy_id filter
   */
  filterByJobVacancyId(id) {
    const vacancyId = parseInt(id, 10);
    if (!vacancyId || isNaN(vacancyId)) {
      return null; // Ignore invalid or missing filter
    }
    return { job_vacancy_id: vacancyId };
  }

  /**
   * Custom filter for the 'status' query parameter.
   * @param {string} status - Status to filter by
   * @returns {Object|null} Where condition for status filter
   */
  filterByStatus(status) {
    if (!status) return null;
    return { status };
  }

  /**
   * Custom filter for the 'search' query parameter.
   * @param {string} search - Search term
   * @returns {Object|null} Where condition for search filter
   */
  filterBySearch(search) {
    if (!search) return null;

    return {
      '$user.name$': {
        [Op.iLike]: `%${search}%`,
      },
    };
  }
}

module.exports = UserJobVacanciesRepository;
