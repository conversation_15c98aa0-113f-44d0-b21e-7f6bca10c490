/* eslint-disable no-process-exit */
const fs = require('fs');
const path = require('path');

const schemaFile = path.join(__dirname, 'models', 'schema.js');
const migrationsDir = path.join(__dirname, 'migrations');

/**
 * Extract migration filename from full path
 * @param {string} filePath - Full path to migration file
 * @returns {string} - Migration filename without extension
 */
function extractMigrationName(filePath) {
  return path.basename(filePath, '.js');
}

/**
 * Get all migration files from the migrations directory
 * @returns {string[]} - Array of migration filenames
 */
function getMigrationFiles() {
  try {
    const files = fs.readdirSync(migrationsDir);
    return files
      .filter(file => file.endsWith('.js'))
      .map(file => extractMigrationName(file))
      .sort();
  } catch (error) {
    console.error('❌ Error reading migrations directory:', error.message);
    return [];
  }
}

/**
 * Load applied migrations from schema.js
 * @returns {string[]} - Array of applied migration names
 */
function getAppliedMigrations() {
  try {
    // Clear require cache to get fresh data
    delete require.cache[require.resolve(schemaFile)];
    const schema = require(schemaFile);

    if (!schema.appliedMigrations) {
      console.warn('⚠️  No appliedMigrations found in schema.js. Run schema-generator.js first.');
      return [];
    }

    // Remove .js extension from applied migrations to match file names
    return schema.appliedMigrations.map(migration => migration.replace(/\.js$/, '')).sort();
  } catch (error) {
    console.error('❌ Error loading schema.js:', error.message);
    console.error('Make sure to run schema-generator.js first to generate the schema file.');
    return [];
  }
}

/**
 * Compare migration files with applied migrations
 */
function verifyMigrations() {
  console.log('🔍 Starting migration verification...\n');

  const migrationFiles = getMigrationFiles();
  const appliedMigrations = getAppliedMigrations();

  if (migrationFiles.length === 0) {
    console.log('📁 No migration files found in src/migrations/');
    return;
  }

  if (appliedMigrations.length === 0) {
    console.log('📄 No applied migrations found in schema.js');
    return;
  }

  console.log(`📁 Found ${migrationFiles.length} migration files`);
  console.log(`📄 Found ${appliedMigrations.length} applied migrations\n`);

  // Find migrations that exist as files but are not applied
  const pendingMigrations = migrationFiles.filter(file => !appliedMigrations.includes(file));

  // Find applied migrations that don't have corresponding files
  const missingFiles = appliedMigrations.filter(migration => !migrationFiles.includes(migration));

  console.log('\n⏳ PENDING MIGRATIONS (files exist but not applied):');
  if (pendingMigrations.length > 0) {
    pendingMigrations.forEach(migration => {
      console.log(`   ⏳ ${migration}`);
    });
  } else {
    console.log('   (none)');
  }

  console.log('\n❌ MISSING MIGRATION FILES (applied but file not found):');
  if (missingFiles.length > 0) {
    missingFiles.forEach(migration => {
      console.log(`   ❌ ${migration}`);
    });
  } else {
    console.log('   (none)');
  }

  // Exit with error code if there are issues
  if (missingFiles.length > 0) {
    console.log('\n⚠️  WARNING: Some applied migrations are missing their files!');
    console.log('This could indicate deleted migration files or database inconsistency.');
    process.exit(1);
  }

  if (pendingMigrations.length > 0) {
    console.log('\n⚠️  WARNING: There are pending migrations that need to be applied.');
    console.log('Run "npm run db:migrate" to apply them.');
    process.exit(1);
  }

  console.log('\n🎉 Migration verification completed!');
}

// Run verification if this file is executed directly
if (require.main === module) {
  verifyMigrations();
}

module.exports = {
  verifyMigrations,
  getMigrationFiles,
  getAppliedMigrations,
  extractMigrationName,
};
