const express = require('express');
const jobTitlesController = require('../controllers/JobTitleController');
const { authenticateToken, requireRole } = require('../middlewares/auth');

const router = express.Router();

/**
 * @route GET /api/v1/job_titles
 * @desc Get all job titles
 * @access Private (Admin)
 */
router.get('/', authenticateToken, requireRole('admin'), jobTitlesController.index);

module.exports = router;
