const express = require('express');
const jobLevelsController = require('../controllers/JobLevelsController');
const { authenticateToken, requireRole } = require('../middlewares/auth');

const router = express.Router();

/**
 * @route GET /api/v1/job_levels
 * @desc Get all job levels
 * @access Private (Admin)
 */
router.get('/', authenticateToken, requireRole('admin'), jobLevelsController.index);

module.exports = router;
