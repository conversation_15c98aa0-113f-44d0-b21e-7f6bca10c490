const express = require('express');
const usersController = require('../controllers/UsersController');
const { authenticateToken, requireRole } = require('../middlewares/auth');

const router = express.Router();

/**
 * @route GET /api/v1/users
 * @desc Get all users (admin only)
 * @access Private (Admin)
 */
router.get('/', authenticateToken, requireRole('admin'), usersController.index);

/**
 * @route GET /api/v1/users/:id
 * @desc Get a single user by ID (admin only)
 * @access Private (Admin)
 */
router.get('/:id', authenticateToken, requireRole('admin'), usersController.show);

module.exports = router;
