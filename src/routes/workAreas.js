const express = require('express');
const workAreasController = require('../controllers/WorkAreasController');
const { authenticateToken, requireRole } = require('../middlewares/auth');

const router = express.Router();

/**
 * @route GET /api/v1/work_areas
 * @desc Get all job levels
 * @access Private (Admin)
 */
router.get('/', authenticateToken, requireRole('admin'), workAreasController.index);

module.exports = router;
