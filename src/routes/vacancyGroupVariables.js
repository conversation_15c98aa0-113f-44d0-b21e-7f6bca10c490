const express = require('express');
const VacancyGroupVariablesController = require('../controllers/VacancyGroupVariablesController');
const { authenticateToken, requireRole } = require('../middlewares/auth');

const vacancyGroupVariablesController = new VacancyGroupVariablesController();

const router = express.Router();

/**
 * Apply admin-only authorization to all routes in this file
 * All vacancy group variable endpoints require admin role
 */
router.use(authenticateToken, requireRole('admin'));

/**
 * @route GET /api/v1/vacancy_group_variables
 * @desc Get all vacancy group variables for a specific job vacancy
 * @access Private (Admin)
 * @query {number} job_vacancy_id - Required. The ID of the job vacancy to filter by
 * @query {string} sort - Sort field (id, match_type, weight, order_level, created_at, updated_at)
 * @query {string} sort_direction - Sort direction (asc, desc)
 * @query {number} page - Page number for pagination
 * @query {number} limit - Number of items per page (max 100)
 */
router.get('/', vacancyGroupVariablesController.index);

/**
 * @route PATCH /api/v1/vacancy_group_variables/bulk_update
 * @desc Bulk update vacancy group variables
 * @access Private (Admin)
 * @body {Object} vacancy_group_variables - Array of vacancy group variables to update
 */
router.patch('/bulk_update', vacancyGroupVariablesController.bulkUpdate);

module.exports = router;
