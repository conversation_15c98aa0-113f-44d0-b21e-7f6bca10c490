const express = require('express');
const userJobVacanciesController = require('../controllers/UserJobVacanciesController');
const { authenticateToken, requireRole } = require('../middlewares/auth');

const router = express.Router();

/**
 * @route GET /api/v1/user_job_vacancies
 * @desc Get all user-job vacancy recommendations, filterable by job_vacancy_id
 * @access Private (Admin)
 * @query {number} job_vacancy_id - The ID of the job vacancy to filter by
 * @query {string} status - Filter by recommendation status (recommended, not_recommended)
 * @query {string} sort - Sort field (id, competency_match, skill_match, status, created_at, updated_at)
 * @query {string} sort_direction - Sort direction (asc, desc)
 * @query {number} page - Page number for pagination
 * @query {number} limit - Number of items per page (max 100)
 */
router.get('/', authenticateToken, requireRole('admin'), userJobVacanciesController.index);

/**
 * @route GET /api/v1/user_job_vacancies/:id
 * @desc Get a specific user-job vacancy recommendation by ID
 * @access Private (Admin)
 * @param {number} id - The ID of the user job vacancy recommendation
 */
router.get('/:id', authenticateToken, requireRole('admin'), userJobVacanciesController.show);

module.exports = router;
