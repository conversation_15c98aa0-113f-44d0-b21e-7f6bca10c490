const express = require('express');
const bonesController = require('../controllers/BonesController');
const { authenticateToken, requireRole } = require('../middlewares/auth');

const router = express.Router();

/**
 * @route GET /api/v1/bones
 * @desc Get all job levels
 * @access Private (Admin)
 */
router.get('/', authenticateToken, requireRole('admin'), bonesController.index);

module.exports = router;
