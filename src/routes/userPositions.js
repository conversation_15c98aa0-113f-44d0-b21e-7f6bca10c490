const express = require('express');
const userPositionsController = require('../controllers/UserPositionsController');
const { authenticateToken, requireRole } = require('../middlewares/auth');

const router = express.Router();

/**
 * @route GET /api/v1/user_positions
 * @desc Get position history for a user
 * @access Private (Admin)
 * @query {number} user_id - The ID of the user to fetch positions for
 */
router.get('/', authenticateToken, requireRole('admin'), userPositionsController.index);

module.exports = router;
