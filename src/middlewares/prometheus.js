const client = require('prom-client');

// Database Calls Counter
const dbCallsTotal = new client.Counter({
  name: 'db_calls_total',
  help: 'Total number of database calls',
  labelNames: ['db_type'],
});

// Database Call Duration Histogram
const dbCallDuration = new client.Histogram({
  name: 'db_call_duration_seconds',
  help: 'Duration of database calls in seconds',
  labelNames: ['db_type'],
  buckets: [0.001, 0.0025, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1],
});

// Create a Registry which registers the metrics
const register = new client.Registry();

// Add a default label which is added to all metrics
register.setDefaultLabels({
  app: 'paragon-app',
});

// Enable the collection of default metrics
client.collectDefaultMetrics({ register });

// Custom metrics based on the Grafana dashboard

// HTTP Request Duration Histogram
const httpRequestDuration = new client.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code', 'endpoint'],
  buckets: [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10],
});

// HTTP Request Counter
const httpRequestsTotal = new client.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status', 'endpoint'],
});

// HTTP Request Rate (RPS)
const httpRequestRate = new client.Gauge({
  name: 'http_request_rate',
  help: 'Current HTTP request rate per second',
  labelNames: ['endpoint'],
});

// HTTP Error Counter
const httpErrorsTotal = new client.Counter({
  name: 'http_errors_total',
  help: 'Total number of HTTP errors (4xx, 5xx)',
  labelNames: ['method', 'route', 'status_code', 'endpoint'],
});

// Apdex Score
const apdexScore = new client.Gauge({
  name: 'apdex_score',
  help: 'Apdex score for application performance',
  labelNames: ['endpoint'],
});

// Register all metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestsTotal);
register.registerMetric(httpRequestRate);
register.registerMetric(httpErrorsTotal);
register.registerMetric(apdexScore);
register.registerMetric(dbCallsTotal);
register.registerMetric(dbCallDuration);
// Helper to record a database call (for use in DB/Redis wrappers)
function recordDbCall(dbType, durationSeconds) {
  dbCallsTotal.labels(dbType).inc();
  if (typeof durationSeconds === 'number') {
    dbCallDuration.labels(dbType).observe(durationSeconds);
  }
}

// Automatic Sequelize metrics wrapper (call this once in your app setup)
function setupSequelizeMetrics(sequelize) {
  if (!sequelize || typeof sequelize.addHook !== 'function') {
    console.warn('Prometheus: Invalid Sequelize instance provided');
    return;
  }

  // Hook into all Sequelize queries
  sequelize.addHook('beforeQuery', options => {
    options._prometheusStartTime = Date.now();
  });

  sequelize.addHook('afterQuery', (options, _result) => {
    if (options._prometheusStartTime) {
      const duration = (Date.now() - options._prometheusStartTime) / 1000;
      recordDbCall('postgresql', duration);
    }
  });

  console.log('Prometheus: Sequelize metrics tracking enabled');
}

// Apdex threshold in seconds (0.5s for web applications)
const APDEX_THRESHOLD = 0.5;

// Store for calculating request rates and Apdex
const requestCounts = new Map();
const errorCounts = new Map();
const apdexCounts = new Map();

// Update rates every 10 seconds
if (process.env.NODE_ENV !== 'test') {
  setInterval(() => {
    updateRatesAndApdex();
  }, 10000);
}

// Middleware to track HTTP requests
const prometheusMiddleware = (req, res, next) => {
  const start = Date.now();

  // Track the request
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.originalUrl;
    const method = req.method;
    const statusCode = res.statusCode.toString();
    const endpoint = `${method} ${route}`;

    // Record basic metrics
    httpRequestDuration.labels(method, route, statusCode, endpoint).observe(duration);

    httpRequestsTotal.labels(method, route, statusCode, endpoint).inc();

    // Track errors (4xx, 5xx)
    if (res.statusCode >= 400) {
      httpErrorsTotal.labels(method, route, statusCode, endpoint).inc();
    }

    // Track Apdex
    trackApdex(duration, endpoint);

    // Update request counts for rate calculation
    requestCounts.set(endpoint, (requestCounts.get(endpoint) || 0) + 1);

    if (res.statusCode >= 400) {
      errorCounts.set(endpoint, (errorCounts.get(endpoint) || 0) + 1);
    }
  });

  next();
};

// Operation name mapping removed: only method, route, endpoint are used for metrics

// Function to track Apdex score (now only by endpoint)
function trackApdex(duration, endpoint) {
  if (!apdexCounts.has(endpoint)) {
    apdexCounts.set(endpoint, { satisfied: 0, tolerating: 0, frustrated: 0, total: 0 });
  }
  const counts = apdexCounts.get(endpoint);
  counts.total++;
  if (duration <= APDEX_THRESHOLD) {
    counts.satisfied++;
  } else if (duration <= APDEX_THRESHOLD * 4) {
    counts.tolerating++;
  } else {
    counts.frustrated++;
  }
}

// Function to update rates and Apdex scores
function updateRatesAndApdex() {
  // Update request rates (RPS)
  for (const [endpoint, count] of requestCounts.entries()) {
    const rps = count / 10; // 10-second intervals
    httpRequestRate.labels(endpoint).set(rps);
    requestCounts.set(endpoint, 0); // Reset counter
  }

  // Reset error counts
  for (const [endpoint] of errorCounts.entries()) {
    errorCounts.set(endpoint, 0);
  }

  // Calculate and update Apdex scores
  for (const [endpoint, counts] of apdexCounts.entries()) {
    if (counts.total > 0) {
      const apdex = (counts.satisfied + counts.tolerating * 0.5) / counts.total;
      apdexScore.labels(endpoint).set(apdex);

      // Reset counters for next period
      counts.satisfied = 0;
      counts.tolerating = 0;
      counts.frustrated = 0;
      counts.total = 0;
    }
  }
}

// Metrics endpoint handler
const metricsHandler = async (_req, res) => {
  try {
    res.set('Content-Type', register.contentType);
    const metrics = await register.metrics();
    res.end(metrics);
  } catch (error) {
    res.status(500).end(error.message);
  }
};

module.exports = {
  register,
  prometheusMiddleware,
  metricsHandler,
  setupSequelizeMetrics,
  recordDbCall,
  metrics: {
    httpRequestDuration,
    httpRequestsTotal,
    httpRequestRate,
    httpErrorsTotal,
    apdexScore,
    dbCallsTotal,
    dbCallDuration,
  },
};
