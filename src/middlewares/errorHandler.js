const InvalidError = require('../errors/InvalidError');
const UnauthorizedError = require('../errors/UnauthorizedError');
const ForbiddenError = require('../errors/ForbiddenError');
const NotFoundError = require('../errors/NotFoundError');

const errorHandler = (error, req, res, next) => {
  if (res.headersSent) {
    return next(error);
  }

  let backtrace = [];
  if (process.env.NODE_ENV !== 'production') {
    backtrace = error.stack ? error.stack.split('\n').map(line => line.trim()) : [];
  }

  // Handle known error types
  if (error instanceof InvalidError) {
    return res.status(400).json({
      error: error.message || 'Bad Request',
      details: error.details || [],
      backtrace,
    });
  }

  if (error instanceof UnauthorizedError) {
    return res.status(401).json({
      error: error.message || 'Unauthorized',
      details: [],
      backtrace,
    });
  }

  if (error instanceof ForbiddenError) {
    return res.status(403).json({
      error: error.message || 'Forbidden',
      details: [],
      backtrace,
    });
  }

  if (error instanceof NotFoundError) {
    return res.status(404).json({
      error: error.message || 'Not Found',
      details: [],
      backtrace,
    });
  }

  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    return res.status(400).json({
      error: 'Invalid request body',
      details: [{ message: error.body }],
      backtrace,
    });
  }

  // Handle Sequelize errors
  if (error.name === 'SequelizeValidationError') {
    const details = error.errors.map(err => ({
      field: err.path,
      message: err.message,
    }));

    return res.status(400).json({
      error: 'Validation failed',
      details,
      backtrace,
    });
  }

  if (error.name === 'SequelizeUniqueConstraintError') {
    return res.status(400).json({
      error: 'Duplicate entry',
      details: [{ field: error.errors[0].path, message: 'Already exists' }],
      backtrace,
    });
  }

  // Handle unexpected errors
  console.error('Unexpected error:', error);
  console.error('Backtrace:', backtrace);

  res.status(500).json({
    error: 'Internal Server Error',
    details: [],
    backtrace,
  });
};

module.exports = errorHandler;
