const rateLimit = require('express-rate-limit');

/**
 * Rate limiting middleware configuration
 * Implements API rate limiting with configurable limits and proper headers
 */

// Default rate limit configuration
const DEFAULT_WINDOW_MS = 15 * 60 * 1000; // 15 minutes
const DEFAULT_MAX_REQUESTS = 100; // requests per window
const DEFAULT_SKIP_SUCCESSFUL_REQUESTS = false;
const DEFAULT_SKIP_FAILED_REQUESTS = false;

/**
 * Create rate limiter with environment-based configuration
 * @param {Object} options - Override options
 * @returns {Function} Express middleware
 */
const createRateLimiter = (options = {}) => {
  // Use options first, then environment variables, then defaults
  const windowMs =
    options.windowMs || parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || DEFAULT_WINDOW_MS;
  const maxRequests =
    options.max || parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || DEFAULT_MAX_REQUESTS;

  const config = {
    windowMs,
    max: maxRequests,
    skipSuccessfulRequests:
      options.skipSuccessfulRequests !== undefined
        ? options.skipSuccessfulRequests
        : process.env.RATE_LIMIT_SKIP_SUCCESSFUL === 'true' || DEFAULT_SKIP_SUCCESSFUL_REQUESTS,
    skipFailedRequests:
      options.skipFailedRequests !== undefined
        ? options.skipFailedRequests
        : process.env.RATE_LIMIT_SKIP_FAILED === 'true' || DEFAULT_SKIP_FAILED_REQUESTS,

    // Enable standard headers and disable legacy headers
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers initially

    // Custom header handler to ensure we include the required headers
    handler: (_req, res) => {
      const resetTime = new Date(Date.now() + windowMs);

      // Set the required headers as per specification
      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': Math.ceil(resetTime.getTime() / 1000).toString(),
      });

      res.status(429).json({
        error: 'Too Many Requests',
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: Math.ceil(windowMs / 1000),
      });
    },

    // Use default key generator to avoid IPv6 issues
    // The default keyGenerator handles IPv6 addresses properly

    // Skip function for health checks and other endpoints that shouldn't be rate limited
    skip: req => {
      // Skip rate limiting for health check endpoint
      if (req.path === '/api/v1/health') {
        return true;
      }

      // Allow custom skip logic via environment variable
      const skipPaths = process.env.RATE_LIMIT_SKIP_PATHS
        ? process.env.RATE_LIMIT_SKIP_PATHS.split(',').map(path => path.trim())
        : [];

      return skipPaths.includes(req.path);
    },
  };

  // Create the rate limiter
  const limiter = rateLimit(config);

  // Wrap the limiter to add custom headers to all responses
  return (req, res, next) => {
    // Store original send method
    const originalSend = res.send;

    // Override send method to add headers
    res.send = function (data) {
      // Add required headers to all responses
      const resetTime = new Date(Date.now() + windowMs);
      const remaining = Math.max(0, maxRequests - (req.rateLimit?.used || 0));

      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': remaining.toString(),
        'X-RateLimit-Reset': Math.ceil(resetTime.getTime() / 1000).toString(),
      });

      return originalSend.call(this, data);
    };

    // Call the original rate limiter
    limiter(req, res, next);
  };
};

// Pre-configured rate limiters for different use cases
const rateLimiters = {
  // General API rate limiter
  general: createRateLimiter(),

  // Strict rate limiter for authentication endpoints
  auth: createRateLimiter({
    windowMs: parseInt(process.env.RATE_LIMIT_AUTH_WINDOW_MS, 10) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_AUTH_MAX_REQUESTS, 10) || 5, // 5 attempts per window
    skipSuccessfulRequests: true, // Don't count successful logins against the limit
  }),

  // Lenient rate limiter for public endpoints
  public: createRateLimiter({
    windowMs: parseInt(process.env.RATE_LIMIT_PUBLIC_WINDOW_MS, 10) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_PUBLIC_MAX_REQUESTS, 10) || 200, // 200 requests per window
  }),
};

module.exports = {
  createRateLimiter,
  rateLimiters,
};
