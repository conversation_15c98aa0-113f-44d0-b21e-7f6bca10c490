'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user_competencies_profilings', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      user_id: {
        type: Sequelize.INTEGER,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      profiling_date: {
        type: Sequelize.DATEONLY,
      },
      assessors: {
        type: Sequelize.ARRAY(Sequelize.STRING),
      },
      profile_as: {
        type: Sequelize.STRING,
      },
      readiness: {
        type: Sequelize.STRING,
      },
      metadata: {
        type: Sequelize.JSONB,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATEONLY,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATEONLY,
      },
    });

    await queryInterface.addIndex('user_competencies_profilings', ['user_id']);
  },
  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('user_competencies_profilings');
  },
};
