'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_vacancies', 'job_level_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'job_levels',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('job_vacancies', 'job_level_id');
  },
};
