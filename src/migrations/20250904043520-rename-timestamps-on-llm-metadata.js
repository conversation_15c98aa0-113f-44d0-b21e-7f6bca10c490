'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, _Sequelize) {
    await queryInterface.renameColumn('llm_metadata', 'createdAt', 'created_at');
    await queryInterface.renameColumn('llm_metadata', 'updatedAt', 'updated_at');
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.renameColumn('llm_metadata', 'created_at', 'createdAt');
    await queryInterface.renameColumn('llm_metadata', 'updated_at', 'updatedAt');
  },
};
