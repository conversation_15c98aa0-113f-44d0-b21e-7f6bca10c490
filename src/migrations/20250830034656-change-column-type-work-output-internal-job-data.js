'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('internal_job_profile_data', 'work_output', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('internal_job_profile_data', 'work_output', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },
};
