'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('job_vacancies', 'job_title_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('job_vacancies', 'job_title_id', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });
  },
};
