'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('users', 'join_date', {
      type: Sequelize.DATEONLY,
    });

    await queryInterface.addColumn('users', 'employment_status', {
      type: Sequelize.STRING,
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('users', 'join_date');
    await queryInterface.removeColumn('users', 'employment_status');
  },
};
