'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('job_vacancies', 'reference_user_ids');

    await queryInterface.addColumn('job_vacancies', 'status', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'draft',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('job_vacancies', 'status');

    await queryInterface.addColumn('job_vacancies', 'reference_user_ids', {
      type: Sequelize.ARRAY(Sequelize.INTEGER),
      allowNull: false,
      defaultValue: [],
    });
  },
};
