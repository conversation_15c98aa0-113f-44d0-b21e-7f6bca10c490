'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('job_variables', 'constants');

    await queryInterface.addColumn('job_variables', 'normalized_baseline', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });

    await queryInterface.addColumn('job_variables', 'variable_type', {
      type: Sequelize.STRING,
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_variables', 'constants', {
      type: Sequelize.JSONB,
      allowNull: false,
      defaultValue: {},
    });

    await queryInterface.removeColumn('job_variables', 'normalized_baseline');
    await queryInterface.removeColumn('job_variables', 'variable_type');
  },
};
