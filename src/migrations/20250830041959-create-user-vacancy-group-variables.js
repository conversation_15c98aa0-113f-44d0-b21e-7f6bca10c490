'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user_vacancy_group_variables', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      vacancy_group_variable_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'vacancy_group_variables',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      average_match_score: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    await queryInterface.addIndex(
      'user_vacancy_group_variables',
      ['user_id', 'vacancy_group_variable_id'],
      {
        unique: true,
        name: 'user_vacancy_group_variables_user_id_vgv_id_unique_constraint',
      },
    );
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('user_vacancy_group_variables');
  },
};
