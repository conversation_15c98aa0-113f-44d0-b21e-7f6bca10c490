'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_vacancies', 'work_area_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'work_areas',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('job_vacancies', 'bone_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'bones',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('job_vacancies', 'relevant_working_experience', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('job_vacancies', 'work_area_id');
    await queryInterface.removeColumn('job_vacancies', 'bone_id');
    await queryInterface.removeColumn('job_vacancies', 'relevant_working_experience');
  },
};
