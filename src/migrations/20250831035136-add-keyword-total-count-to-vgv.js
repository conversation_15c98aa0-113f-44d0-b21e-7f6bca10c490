'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('vacancy_group_variables', 'keyword_total_count', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('vacancy_group_variables', 'keyword_total_count');
  },
};
