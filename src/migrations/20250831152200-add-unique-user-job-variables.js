'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, _Sequelize) {
    await queryInterface.addIndex('user_job_variables', ['user_id', 'job_variable_id'], {
      unique: true,
      name: 'user_job_variables_user_id_jv_id_unique_constraint',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeIndex(
      'user_job_variables',
      'user_job_variables_user_id_jv_id_unique_constraint',
    );
  },
};
