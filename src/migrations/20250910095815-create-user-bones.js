'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user_bones', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      bone_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'bones',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Add indexes
    await queryInterface.addIndex('user_bones', ['user_id'], {
      name: 'user_bones_user_id_index',
    });

    await queryInterface.addIndex('user_bones', ['bone_id'], {
      name: 'user_bones_bone_id_index',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('user_bones');
  },
};
