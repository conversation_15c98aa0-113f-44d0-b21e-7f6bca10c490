'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user_job_vacancies', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      job_vacancy_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'job_vacancies',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      competency_match: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      },
      skill_match: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      },
      status: {
        type: Sequelize.ENUM('recommended', 'not_recommended'),
        allowNull: false,
        defaultValue: 'not_recommended',
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    await queryInterface.addIndex('user_job_vacancies', ['user_id']);
    await queryInterface.addIndex('user_job_vacancies', ['job_vacancy_id']);
    await queryInterface.addIndex('user_job_vacancies', ['status']);
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('user_job_vacancies');
  },
};
