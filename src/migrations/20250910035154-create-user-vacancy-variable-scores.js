'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user_vacancy_variable_scores', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      job_vacancy_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'job_vacancies',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      job_variable_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'job_variables',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      match_score: {
        type: Sequelize.DOUBLE,
        allowNull: false,
        defaultValue: 0,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    await queryInterface.addIndex(
      'user_vacancy_variable_scores',
      ['user_id', 'job_vacancy_id', 'job_variable_id'],
      {
        unique: true,
        name: 'user_vacancy_variable_scores_uid_jvac_id_jvar_id_unique_constraint',
      },
    );
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('user_vacancy_variable_scores');
  },
};
