'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user_positions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      role_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      department: { type: Sequelize.STRING },
      job_grade: { type: Sequelize.STRING },
      starts_at: { type: Sequelize.DATEONLY },
      ends_at: { type: Sequelize.DATEONLY },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex('user_positions', ['user_id']);
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('user_positions');
  },
};
