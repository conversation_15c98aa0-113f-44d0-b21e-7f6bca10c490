'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_vacancies', 'job_desc', {
      type: Sequelize.ARRAY(Sequelize.STRING),
      allowNull: false,
      defaultValue: [],
    });

    await queryInterface.addColumn('job_vacancies', 'ksao', {
      type: Sequelize.JSONB,
      allowNull: false,
      defaultValue: {
        knowledges: [],
        skills: [],
        abilities: [],
        other_characteristics: [],
      },
    });

    await queryInterface.addColumn('job_vacancies', 'reference_user_ids', {
      type: Sequelize.ARRAY(Sequelize.INTEGER),
      allowNull: false,
      defaultValue: [],
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('job_vacancies', 'job_desc');
    await queryInterface.removeColumn('job_vacancies', 'ksao');
    await queryInterface.removeColumn('job_vacancies', 'reference_user_ids');
  },
};
