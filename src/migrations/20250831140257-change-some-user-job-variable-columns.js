'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('user_job_variables', 'constant');
    await queryInterface.removeColumn('user_job_variables', 'match_score');

    await queryInterface.addColumn('user_job_variables', 'raw_value', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('user_job_variables', 'normalized_value', {
      type: Sequelize.DOUBLE,
      allowNull: false,
      defaultValue: 0,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn('user_job_variables', 'constant', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });

    await queryInterface.addColumn('user_job_variables', 'match_score', {
      type: Sequelize.FLOAT,
      allowNull: false,
      defaultValue: 0,
    });

    await queryInterface.removeColumn('user_job_variables', 'raw_value');
    await queryInterface.removeColumn('user_job_variables', 'normalized_value');
  },
};
