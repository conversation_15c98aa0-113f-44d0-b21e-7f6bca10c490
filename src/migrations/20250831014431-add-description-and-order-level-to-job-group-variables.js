'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_group_variables', 'description', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('job_group_variables', 'order_level', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('job_group_variables', 'description');
    await queryInterface.removeColumn('job_group_variables', 'order_level');
  },
};
