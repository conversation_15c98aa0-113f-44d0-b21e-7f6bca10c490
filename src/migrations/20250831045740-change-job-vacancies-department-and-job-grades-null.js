'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('job_vacancies', 'department', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.changeColumn('job_vacancies', 'job_grade', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('job_vacancies', 'department', {
      type: Sequelize.STRING,
      allowNull: false,
    });

    await queryInterface.changeColumn('job_vacancies', 'job_grade', {
      type: Sequelize.STRING,
      allowNull: false,
    });
  },
};
