'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('internal_job_profile_data', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      job_division: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      job_group: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      position_name: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      job_classification: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      job_family: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      sub_job_family: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      main_responsibilities: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      work_input: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      work_output: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      success_criteria: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      requirement: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      competency: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    await queryInterface.addIndex('internal_job_profile_data', ['position_name'], {
      unique: false,
      name: 'internal_job_profile_data_position_name_index',
    });
  },

  async down(queryInterface) {
    await queryInterface.removeIndex(
      'internal_job_profile_data',
      'internal_job_profile_data_position_name_index',
    );
    await queryInterface.dropTable('internal_job_profile_data');
  },
};
