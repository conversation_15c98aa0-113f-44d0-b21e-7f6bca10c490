'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, _Sequelize) {
    await queryInterface.addIndex('job_variables', ['name'], {
      unique: true,
      name: 'job_variables_name_unique_constraint',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeIndex('job_variables', 'job_variables_name_unique_constraint');
  },
};
