'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('vacancy_group_variables', 'filters', {
      type: Sequelize.JSONB,
      allowNull: false,
      defaultValue: {},
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('vacancy_group_variables', 'filters');
  },
};
