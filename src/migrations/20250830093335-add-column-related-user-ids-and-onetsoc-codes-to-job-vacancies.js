'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_vacancies', 'related_user_ids', {
      type: Sequelize.ARRAY(Sequelize.INTEGER),
      allowNull: true,
    });

    await queryInterface.addColumn('job_vacancies', 'related_onetsoc_codes', {
      type: Sequelize.ARRAY(Sequelize.STRING),
      allowNull: true,
    });
  },

  async down(queryInterface) {
    await queryInterface.removeColumn('job_vacancies', 'related_user_ids');
    await queryInterface.removeColumn('job_vacancies', 'related_onetsoc_codes');
  },
};
