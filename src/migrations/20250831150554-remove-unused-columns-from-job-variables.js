'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('job_variables', 'object_table');
    await queryInterface.removeColumn('job_variables', 'object_column');
    await queryInterface.removeColumn('job_variables', 'object_selector');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_variables', 'object_table', {
      type: Sequelize.STRING,
      allowNull: false,
    });

    await queryInterface.addColumn('job_variables', 'object_column', {
      type: Sequelize.STRING,
      allowNull: false,
    });

    await queryInterface.addColumn('job_variables', 'object_selector', {
      type: Sequelize.JSONB,
      allowNull: false,
    });
  },
};
