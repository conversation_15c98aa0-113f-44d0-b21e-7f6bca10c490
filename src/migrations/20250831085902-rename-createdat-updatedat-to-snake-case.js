'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, _Sequelize) {
    queryInterface.renameColumn('user_competencies_profilings', 'createdAt', 'created_at');
    queryInterface.renameColumn('user_competencies_profilings', 'updatedAt', 'updated_at');

    queryInterface.renameColumn('user_performance_reviews', 'createdAt', 'created_at');
    queryInterface.renameColumn('user_performance_reviews', 'updatedAt', 'updated_at');

    queryInterface.renameColumn('user_assessment_results', 'createdAt', 'created_at');
    queryInterface.renameColumn('user_assessment_results', 'updatedAt', 'updated_at');
  },

  async down(queryInterface, _Sequelize) {
    queryInterface.renameColumn('user_competencies_profilings', 'created_at', 'createdAt');
    queryInterface.renameColumn('user_competencies_profilings', 'updated_at', 'updatedAt');

    queryInterface.renameColumn('user_performance_reviews', 'created_at', 'createdAt');
    queryInterface.renameColumn('user_performance_reviews', 'updated_at', 'updatedAt');

    queryInterface.renameColumn('user_assessment_results', 'created_at', 'createdAt');
    queryInterface.renameColumn('user_assessment_results', 'updated_at', 'updatedAt');
  },
};
