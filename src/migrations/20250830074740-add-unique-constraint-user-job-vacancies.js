'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, _Sequelize) {
    await queryInterface.addIndex('user_job_vacancies', ['user_id', 'job_vacancy_id'], {
      unique: true,
      name: 'user_job_vacancies_user_id_job_vacancy_id_unique_constraint',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeIndex(
      'user_job_vacancies',
      'user_job_vacancies_user_id_job_vacancy_id_unique_constraint',
    );
  },
};
