'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('job_variables', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      job_group_variable_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'job_group_variables',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      object_table: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      object_column: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      object_selector: {
        type: Sequelize.JSONB,
        allowNull: false,
      },
      constants: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {},
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    await queryInterface.addIndex('job_variables', ['job_group_variable_id']);
    await queryInterface.addIndex(
      'job_variables',
      ['object_table', 'object_column', 'object_selector'],
      {
        unique: true,
        name: 'job_variables_object_table_column_selector_unique_constraint',
      },
    );
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('job_variables');
  },
};
