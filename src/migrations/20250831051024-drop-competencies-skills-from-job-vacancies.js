'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('job_vacancies', 'competencies');
    await queryInterface.removeColumn('job_vacancies', 'skills');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_vacancies', 'competencies', {
      type: Sequelize.ARRAY(Sequelize.STRING),
      allowNull: true,
      defaultValue: null,
    });

    await queryInterface.addColumn('job_vacancies', 'skills', {
      type: Sequelize.ARRAY(Sequelize.STRING),
      allowNull: true,
      defaultValue: null,
    });
  },
};
