'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user_job_variables', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      job_variable_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'job_variables',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      constant: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      match_score: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    await queryInterface.addIndex('user_job_variables', ['user_id']);
    await queryInterface.addIndex('user_job_variables', ['job_variable_id']);

    await queryInterface.addIndex(
      'user_job_variables',
      ['user_id', 'job_variable_id', 'constant'],
      {
        unique: true,
        name: 'user_job_variables_user_id_jv_id_constant_unique_constraint',
      },
    );
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('user_job_variables');
  },
};
