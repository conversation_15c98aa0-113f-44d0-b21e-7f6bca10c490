'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user_profiles', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      phone_number: { type: Sequelize.STRING },
      location: { type: Sequelize.STRING },
      manager: { type: Sequelize.STRING },
      current_position: { type: Sequelize.JSONB },
      years_experience: { type: Sequelize.INTEGER },
      performance_rating: { type: Sequelize.FLOAT },
      last_promotion: { type: Sequelize.DATEONLY },
      education: { type: Sequelize.STRING },
      competencies: { type: Sequelize.ARRAY(Sequelize.STRING) },
      skills: { type: Sequelize.ARRAY(Sequelize.STRING) },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex('user_profiles', ['user_id']);
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('user_profiles');
  },
};
