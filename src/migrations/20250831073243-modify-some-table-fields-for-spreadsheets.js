'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Change user_assessment_results columns to TEXT
    await queryInterface.changeColumn('user_assessment_results', 'assessment', {
      type: Sequelize.TEXT,
    });
    await queryInterface.changeColumn('user_assessment_results', 'aspect_name', {
      type: Sequelize.TEXT,
    });
    await queryInterface.changeColumn('user_assessment_results', 'value_type', {
      type: Sequelize.TEXT,
    });
    await queryInterface.changeColumn('user_assessment_results', 'value', {
      type: Sequelize.TEXT,
    });

    // Change user_competencies_profilings.profiling_date to STRING
    await queryInterface.changeColumn('user_competencies_profilings', 'profiling_date', {
      type: Sequelize.STRING,
    });
  },
  async down(queryInterface, Sequelize) {
    // Revert user_assessment_results columns to STRING(255)
    await queryInterface.changeColumn('user_assessment_results', 'assessment', {
      type: Sequelize.STRING(255),
    });
    await queryInterface.changeColumn('user_assessment_results', 'aspect_name', {
      type: Sequelize.STRING(255),
    });
    await queryInterface.changeColumn('user_assessment_results', 'value_type', {
      type: Sequelize.STRING(255),
    });
    await queryInterface.changeColumn('user_assessment_results', 'value', {
      type: Sequelize.STRING(255),
    });

    // Revert user_competencies_profilings.profiling_date to DATEONLY
    await queryInterface.changeColumn('user_competencies_profilings', 'profiling_date', {
      type: Sequelize.STRING(255),
    });
  },
};
