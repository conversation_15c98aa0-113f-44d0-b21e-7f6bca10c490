'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('user_positions', 'company_name', {
      type: Sequelize.STRING
    });

    await queryInterface.addColumn('user_positions', 'work_area', {
      type: Sequelize.STRING
    });``

    await queryInterface.addColumn('user_positions', 'division', {
      type: Sequelize.STRING
    });

    await queryInterface.addColumn('user_positions', 'directorate', {
      type: Sequelize.STRING
    });

    await queryInterface.addColumn('user_positions', 'grade_group', {
      type: Sequelize.STRING
    });

    await queryInterface.addColumn('user_positions', 'last_education', {
      type: Sequelize.STRING
    });

    await queryInterface.addColumn('user_positions', 'major', {
      type: Sequelize.STRING
    });

    await queryInterface.addColumn('user_positions', 'sub_grade', {
      type: Sequelize.STRING
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('user_positions', 'company_name');
    await queryInterface.removeColumn('user_positions', 'work_area');
    await queryInterface.removeColumn('user_positions', 'division');
    await queryInterface.removeColumn('user_positions', 'directorate');
    await queryInterface.removeColumn('user_positions', 'grade_group');
    await queryInterface.removeColumn('user_positions', 'last_education');
    await queryInterface.removeColumn('user_positions', 'major');
    await queryInterface.removeColumn('user_positions', 'sub_grade');
  }
};