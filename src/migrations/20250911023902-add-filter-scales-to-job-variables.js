'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_variables', 'filter_scales', {
      type: Sequelize.JSONB,
      allowNull: false,
      defaultValue: {},
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('job_variables', 'filter_scales');
  },
};
