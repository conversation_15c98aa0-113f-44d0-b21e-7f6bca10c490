require('dotenv').config({ quiet: true });

module.exports = {
  // Server configuration
  port: process.env.PORT || 3000,
  host: process.env.HOST || '0.0.0.0',
  corsOrigin: process.env.CORS_ORIGIN || '*',
  nodeEnv: process.env.NODE_ENV || 'development',

  // JWT configuration
  jwtSecret: process.env.JWT_SECRET,
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1h',
  saltRounds: parseInt(process.env.SALT_ROUNDS, 10) || 12,

  // Rate limiting configuration
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000, // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100,
    skipSuccessful: process.env.RATE_LIMIT_SKIP_SUCCESSFUL === 'true',
    skipFailed: process.env.RATE_LIMIT_SKIP_FAILED === 'true',
    skipPaths: process.env.RATE_LIMIT_SKIP_PATHS
      ? process.env.RATE_LIMIT_SKIP_PATHS.split(',').map(path => path.trim())
      : [],

    // Authentication specific limits
    auth: {
      windowMs: parseInt(process.env.RATE_LIMIT_AUTH_WINDOW_MS, 10) || 15 * 60 * 1000,
      maxRequests: parseInt(process.env.RATE_LIMIT_AUTH_MAX_REQUESTS, 10) || 5,
    },

    // Public endpoints limits
    public: {
      windowMs: parseInt(process.env.RATE_LIMIT_PUBLIC_WINDOW_MS, 10) || 15 * 60 * 1000,
      maxRequests: parseInt(process.env.RATE_LIMIT_PUBLIC_MAX_REQUESTS, 10) || 200,
    },
  },

  // Gemini
  geminiApiKey: process.env.GEMINI_API_KEY,

  // Qdrant
  qdrantUrl: process.env.QDRANT_URL,
  qdrantApiKey: process.env.QDRANT_API_KEY,

  // Misc
  enableSequelizeMetrics: process.env.ENABLE_SEQUELIZE_METRICS === 'true',
  jobDescWithOnet: process.env.JOB_DESC_WITH_ONET === 'true',
};
