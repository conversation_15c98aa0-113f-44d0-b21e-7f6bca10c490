/**
 * Base class for all output formatting classes
 * Provides common output formatting functionality
 */
class ApiOutput {
  constructor(data, options = {}) {
    this.data = data;
    this.options = options;
  }

  /**
   * Get pagination output
   * @returns {Object} Pagination data
   */
  paginationOutput() {
    const pagination = this.options.pagination || {};
    const page = pagination.page;
    const limit = pagination.limit;
    const total = pagination.total || this.data.length;

    let totalPages;
    if (!!limit && !!total) {
      totalPages = Math.ceil(total / limit);
    }

    return {
      page,
      limit,
      total,
      totalPages,
    };
  }

  /**
   * Render the output data as JSON response
   * @param {Object} res - Express response object
   */
  renderJson(res) {
    const status = this.options.statusCode || 200;
    const formatMethod = this.options.formatMethod || 'format';

    const data = this[formatMethod]();
    res.status(status).json({ data });
  }

  /**
   * Render the output data as JSON response
   * @param {Object} res - Express response object
   */
  renderJsonArray(res) {
    const status = this.options.statusCode || 200;
    const formatMethod = this.options.formatMethod || 'format';

    const data = this.data.map(item => {
      const output = new this.constructor(item, this.options);
      return output[formatMethod]();
    });

    const pagination = this.paginationOutput();
    res.status(status).json({ data, pagination });
  }
}

module.exports = ApiOutput;
