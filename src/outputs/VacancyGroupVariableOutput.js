const ApiOutput = require('./ApiOutput');

class VacancyGroupVariableOutput extends ApiOutput {
  /**
   * Format a single vacancy group variable for output
   * @returns {Object} Formatted vacancy group variable data
   */
  format() {
    const vgv = this.data;

    return {
      id: vgv.id,
      job_group_variable: this.jobGroupVariableOutput(),
      keyword_match_count: vgv.keyword_match_count,
      keyword_total_count: vgv.keyword_total_count,
      match_type: vgv.match_type,
      weight: vgv.weight,
      order_level: vgv.jobGroupVariable?.order_level,
      bone_value: vgv.bone_value,
      ...this.filtersOutput(),
    };
  }

  jobGroupVariableOutput() {
    if (!this.data.jobGroupVariable) return {};

    return {
      id: this.data.jobGroupVariable.id,
      name: this.data.jobGroupVariable.name,
      description: this.data.jobGroupVariable.description,
    };
  }

  filtersOutput() {
    const output = {
      filters: [],
      avg_baseline: 0,
    };

    if (!this.data.jobGroupVariable) return output;

    const jobVariables = this.data.jobGroupVariable.jobVariables || [];
    const baselineValues = this.options.baselineValues || {};
    const validBaselineValues = [];

    output.filters = jobVariables.map(jobVariable => {
      const baselineValue = parseInt(baselineValues[jobVariable.id], 10);
      const filterScales = jobVariable.filter_scales || {};
      const filterValue = filterScales[baselineValue] || null;

      if (baselineValue > 0) {
        validBaselineValues.push(baselineValue);
      }

      return {
        job_variable_id: jobVariable.id,
        job_variable_name: jobVariable.name,
        job_variable_type: jobVariable.variable_type,
        value: filterValue,
      };
    });

    if (validBaselineValues.length > 0) {
      output.avg_baseline = Math.round(
        validBaselineValues.reduce((a, b) => a + b, 0) / parseFloat(validBaselineValues.length),
      );
    }

    return output;
  }
}

module.exports = VacancyGroupVariableOutput;
