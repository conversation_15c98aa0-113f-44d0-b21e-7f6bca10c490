const ApiOutput = require('./ApiOutput');

class UserJobVacancyOutput extends ApiOutput {
  /**
   * Format the UserJobVacancy data for the API response.
   * @param {Object} item - A UserJobVacancy model instance with nested user and profile data (for array formatting)
   * @returns {Object} Formatted data.
   */
  format() {
    const record = this.data;
    const user = record.user || {};

    return {
      id: record.id,
      user: {
        id: user.id,
        name: user.name,
      },
      status: record.status,
      match_rate: Math.floor(record.match_rate),
      variable_groups: this.variableGroupsOutput(),
    };
  }

  variableGroupsOutput() {
    const groupScoresById = this.options.groupScoresById || {};
    const currentData = groupScoresById[this.data.id] || [];

    return currentData.map(group => {
      const variables = group.variables || [];

      return {
        id: group.id,
        name: group.name,
        avg_match_score: Math.round(group.avg_match_score * 100) / 100,
        variables: variables.map(variable => ({
          id: variable.id,
          name: variable.name,
          raw_value: variable.raw_value || '-',
        })),
      };
    });
  }
}

module.exports = UserJobVacancyOutput;
