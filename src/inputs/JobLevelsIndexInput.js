const ApplicationInput = require('./ApplicationInput');

class JobLevelsIndexInput extends ApplicationInput {
  /**
   * Define the JSON schema for job levels index validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        search: {
          type: 'string',
        },
        sort: {
          type: 'string',
          enum: ['order_level'],
        },
        sort_direction: {
          type: 'string',
          enum: ['asc', 'desc'],
        },
        page: {
          type: 'integer',
          minimum: 1,
        },
        limit: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
        },
      },
      required: [],
    };
  }
}

module.exports = JobLevelsIndexInput;
