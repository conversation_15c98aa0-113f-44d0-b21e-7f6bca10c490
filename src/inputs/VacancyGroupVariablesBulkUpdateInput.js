const ApplicationInput = require('./ApplicationInput');

class VacancyGroupVariablesBulkUpdateInput extends ApplicationInput {
  /**
   * Define the JSON schema for vacancy group variables bulk update validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        vacancy_group_variables: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: {
                type: 'integer',
                minimum: 1,
              },
              match_type: {
                type: 'string',
                enum: ['filter', 'weight'],
              },
              weight: {
                type: 'number',
                minimum: 0,
              },
              // filters: {
              //   type: 'array',
              //   items: {
              //     type: 'object',
              //     properties: {
              //       job_variable_id: {
              //         type: 'integer',
              //         minimum: 1,
              //       },
              //       job_variable_name: {
              //         type: 'string',
              //       },
              //       value: {
              //         oneOf: [{ type: 'number' }, { type: 'string' }],
              //       },
              //     },
              //     required: ['job_variable_id', 'job_variable_name', 'value'],
              //     additionalProperties: false,
              //   },
              //   default: [],
              // },
            },
            required: ['id', 'match_type'],
            additionalProperties: false,
          },
          minItems: 1,
        },
      },
      required: ['vacancy_group_variables'],
      additionalProperties: false,
    };
  }

  /**
   * Custom validation to ensure weight is provided for weight match type
   * and filters are provided for filter match type
   */
  validate() {
    // First run the base validation
    super.validate();

    // Custom validation for match_type specific requirements
    const data = this.validatedData;

    if (data && data.vacancy_group_variables) {
      for (const vgv of data.vacancy_group_variables) {
        if (vgv.match_type === 'weight' && (vgv.weight === undefined || vgv.weight === null)) {
          const InvalidError = require('../errors/InvalidError');
          throw new InvalidError('Weight is required when match_type is "weight"');
        }
      }
    }

    return true;
  }
}

module.exports = VacancyGroupVariablesBulkUpdateInput;
