const ApplicationInput = require('./ApplicationInput');

class BonesIndexInput extends ApplicationInput {
  schema() {
    return {
      type: 'object',
      properties: {
        sort: {
          type: 'string',
          enum: ['id', 'name', 'created_at', 'updated_at'],
        },
        sort_direction: {
          type: 'string',
          enum: ['asc', 'desc'],
        },
        page: {
          type: 'integer',
          minimum: 1,
        },
        limit: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
        },
      },
      required: [],
    };
  }
}

module.exports = BonesIndexInput;
