const ApplicationInput = require('./ApplicationInput');

class VacancyGroupVariablesIndexInput extends ApplicationInput {
  /**
   * Define the JSON schema for vacancy group variables index validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        job_vacancy_id: {
          type: 'integer',
          minimum: 1,
        },
        sort: {
          type: 'string',
          enum: ['id', 'match_type', 'weight', 'order_level', 'created_at', 'updated_at'],
        },
        sort_direction: {
          type: 'string',
          enum: ['asc', 'desc'],
        },
        page: {
          type: 'integer',
          minimum: 1,
        },
        limit: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
        },
      },
      required: ['job_vacancy_id'],
      additionalProperties: false,
    };
  }
}

module.exports = VacancyGroupVariablesIndexInput;
