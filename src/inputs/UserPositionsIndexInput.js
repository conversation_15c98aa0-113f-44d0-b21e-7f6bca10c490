const ApplicationInput = require('./ApplicationInput');

class UserPositionsIndexInput extends ApplicationInput {
  /**
   * Define the JSON schema for listing user positions validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        user_id: {
          type: 'integer',
          minimum: 1,
        },
        sort: {
          type: 'string',
          enum: ['id', 'role_name', 'created_at', 'updated_at'],
        },
        sort_direction: {
          type: 'string',
          enum: ['asc', 'desc'],
        },
        page: {
          type: 'integer',
          minimum: 1,
        },
        limit: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
        },
      },
      required: ['user_id'],
    };
  }
}

module.exports = UserPositionsIndexInput;
