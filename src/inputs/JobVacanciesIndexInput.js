const ApplicationInput = require('./ApplicationInput');

class JobVacanciesIndexInput extends ApplicationInput {
  /**
   * Define the JSON schema for job vacancies index validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        search: {
          type: 'string',
        },
        sort: {
          type: 'string',
          enum: ['id', 'name', 'department', 'job_grade', 'created_at', 'updated_at'],
        },
        sort_direction: {
          type: 'string',
          enum: ['asc', 'desc'],
        },
        status: {
          type: 'string',
          enum: [
            'draft',
            'active',
            'generating_jobdesc',
            'generating_job_variables',
            'calculating_match_scores',
          ],
        },
        page: {
          type: 'integer',
          minimum: 1,
        },
        limit: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
        },
      },
      required: [],
      additionalProperties: false,
    };
  }
}

module.exports = JobVacanciesIndexInput;
