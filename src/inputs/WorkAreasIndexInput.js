const ApplicationInput = require('./ApplicationInput');

class WorkAreasIndexInput extends ApplicationInput {
  /**
   * Define the JSON schema for work areas index validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        sort: {
          type: 'string',
          enum: ['id', 'name', 'created_at', 'updated_at', 'order_level'],
        },
        sort_direction: {
          type: 'string',
          enum: ['asc', 'desc'],
        },
        page: {
          type: 'integer',
          minimum: 1,
        },
        limit: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
        },
      },
      required: [],
    };
  }
}

module.exports = WorkAreasIndexInput;
