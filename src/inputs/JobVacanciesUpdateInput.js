const ApplicationInput = require('./ApplicationInput');

class JobVacanciesUpdateInput extends ApplicationInput {
  /**
   * Get the JSON schema for validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        name: {
          type: 'string',
        },
        job_desc: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
        reference_user_ids: {
          type: 'array',
          items: {
            type: 'integer',
          },
        },
        followup_action: {
          type: 'string',
          enum: ['generate_jobdesc', 'generate_job_variables'],
        },
        detailed_descriptions: {
          type: 'object',
          properties: {
            key_responsibilities: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
            qualifications: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
            competencies: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
            success_metrics: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
          },
        },
        job_level_id: {
          type: 'integer',
        },
        work_area_id: {
          type: 'integer',
        },
        bone_id: {
          type: 'integer',
        },
        relevant_working_experience: {
          type: 'boolean',
        },
        role_summary: {
          type: 'string',
        },
      },
      required: [],
      additionalProperties: false,
    };
  }

  output() {
    const data = super.output();

    data.related_user_ids = data.reference_user_ids;
    delete data.reference_user_ids;

    return data;
  }
}

module.exports = JobVacanciesUpdateInput;
