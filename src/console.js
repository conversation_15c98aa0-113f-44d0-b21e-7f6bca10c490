#!/usr/bin/env node
/* eslint-disable no-process-exit */

require('dotenv').config({ quiet: true });

const repl = require('repl');
const util = require('util');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Define the path for the history file
// Using the home directory is standard practice for user-specific config/history files.
const HISTORY_FILE = path.join(os.homedir(), '.paragon_api_console_history');

// Import core components
const config = require('./config/config');
const { sequelize } = require('./config/sequelize');

// Import all models
const models = require('./models');

// Import base classes
const AppModel = require('./models/AppModel');
const BaseRepository = require('./repositories/BaseRepository');
const AppService = require('./services/AppService');

// Import all repositories
const repositories = {};
const repositoriesPath = path.join(__dirname, 'repositories');
fs.readdirSync(repositoriesPath)
  .filter(file => file !== 'BaseRepository.js' && file.endsWith('.js'))
  .forEach(file => {
    const repositoryName = path.basename(file, '.js');
    const Repository = require(path.join(repositoriesPath, file));
    repositories[repositoryName] = Repository;
  });

// Import all services
const services = {};
const servicesPath = path.join(__dirname, 'services');
fs.readdirSync(servicesPath)
  .filter(file => file !== 'AppService.js' && file.endsWith('.js'))
  .forEach(file => {
    const serviceName = path.basename(file, '.js');
    const Service = require(path.join(servicesPath, file));
    services[serviceName] = Service;
  });

// Import error classes
const errors = {};
const errorsPath = path.join(__dirname, 'errors');
if (fs.existsSync(errorsPath)) {
  fs.readdirSync(errorsPath)
    .filter(file => file.endsWith('.js'))
    .forEach(file => {
      const errorName = path.basename(file, '.js');
      const ErrorClass = require(path.join(errorsPath, file));
      errors[errorName] = ErrorClass;
    });
}

// Create console context
const consoleContext = {
  // Core components
  config,
  sequelize,

  // Models (both individual and collection)
  models,
  ...models,

  // Base classes
  AppModel,
  BaseRepository,
  AppService,

  // Repositories (both classes and instances)
  repositories,
  ...Object.keys(repositories).reduce((acc, name) => {
    acc[name] = repositories[name];
    return acc;
  }, {}),

  // Services (both classes and instances)
  services,
  ...Object.keys(services).reduce((acc, name) => {
    acc[name] = services[name];
    return acc;
  }, {}),

  // Error classes
  errors,
  ...errors,

  // Utility functions
  reload: () => {
    console.log('Reloading console...');
    process.exit(0);
  },

  help: () => {
    console.log(`
Paragon API Console - Rails-style REPL
=====================================

Available Components:
---------------------

Models:
${Object.keys(models)
  .map(name => `  ${name}`)
  .join('\n')}

Repositories:
${Object.keys(repositories)
  .map(name => `  ${name}`)
  .join('\n')}

Services:
${Object.keys(services)
  .map(name => `  ${name}`)
  .join('\n')}

Error Classes:
${Object.keys(errors)
  .map(name => `  ${name}`)
  .join('\n')}

Core Objects:
  config     - Application configuration
  sequelize  - Sequelize instance
  models     - All models collection

Base Classes:
  AppModel      - Base model class
  BaseRepository - Base repository class
  AppService    - Base service class

Utility Functions:
  help()     - Show this help message
  reload()   - Reload the console
  load(path) - Load and execute a JavaScript file in the current context
  .exit      - Exit the console

Examples:
---------
  // Query users
  await User.findAll()

  // Use repository
  const userRepo = new UsersRepository(User)
  await userRepo.findAll({ page: 1, limit: 5 })

  // Use service
  const userService = new UserService()
  await userService.findById(1)

  // Load a script from the project root
  load('my-custom-script.js')

  // Database operations
  await sequelize.authenticate()

  // Check configuration
  config.nodeEnv
`);
  },
};

async function startConsole() {
  console.log('🚀 Starting Paragon API Console...');

  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established');
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
  }

  console.log(`📦 Environment: ${config.nodeEnv}`);
  console.log(
    `🔧 Loaded ${Object.keys(models).length} models, ${Object.keys(repositories).length} repositories, ${Object.keys(services).length} services`,
  );
  console.log('💡 Type "help()" for available commands and examples');
  console.log('');

  // Start REPL
  const replServer = repl.start({
    prompt: `paragon-api (${config.nodeEnv})> `,
    useColors: true,
    useGlobal: false,
    ignoreUndefined: true,
    replMode: repl.REPL_MODE_STRICT,
    writer: output => {
      // Pretty print objects
      if (typeof output === 'object' && output !== null) {
        return util.inspect(output, {
          colors: true,
          depth: 3,
          maxArrayLength: 10,
          maxStringLength: 100,
          compact: false,
        });
      }
      return output;
    },
  });

  // This will load history from the file on start, and save history to the file on exit.
  replServer.setupHistory(HISTORY_FILE, (err, _repl) => {
    if (err) {
      console.error('Error setting up REPL history:', err);
    }
  });

  // Add context to REPL
  Object.assign(replServer.context, consoleContext);

  replServer.context.load = filePath => {
    if (!filePath) {
      console.error("Usage: load('path/to/script.js')");
      return;
    }
    try {
      const absolutePath = path.resolve(process.cwd(), filePath);
      if (!fs.existsSync(absolutePath)) {
        console.error(`Error: File not found at ${absolutePath}`);
        return;
      }

      console.log(`Loading script: ${absolutePath}`);
      const scriptContent = fs.readFileSync(absolutePath, 'utf8');

      // Use the REPL's own eval command to run the script
      // This ensures it runs in the correct, active context.
      replServer.eval(scriptContent, replServer.context, absolutePath, (err, _result) => {
        if (err) {
          // The REPL will automatically display the error in a formatted way
          // but we can log it here as well if needed.
          console.error(`\nError executing script: ${filePath}`);
        }
        // The REPL server will print the result of the last expression
        // automatically, so we don't need to do anything with `result`.
      });
    } catch (error) {
      console.error(`\nError loading script: ${filePath}`);
      console.error(error);
    }
  };

  // Handle exit
  replServer.on('exit', () => {
    console.log('\n👋 Goodbye!');
    process.exit(0);
  });

  // Handle errors
  replServer.on('error', error => {
    console.error('REPL Error:', error);
  });
}

// Handle uncaught exceptions
process.on('uncaughtException', error => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the console
if (require.main === module) {
  startConsole().catch(error => {
    console.error('Failed to start console:', error);
    process.exit(1);
  });
}

module.exports = { startConsole, consoleContext };
