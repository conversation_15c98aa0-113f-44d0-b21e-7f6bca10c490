name: autoupdate
on:
  push: {}
jobs:
  autoupdate:
    name: autoupdate
    runs-on: ubuntu-latest
    steps:
      - uses: docker://chinthakagodawita/autoupdate-action:v1
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
          EXCLUDED_LABELS: "dependencies,wontfix"
          DRY_RUN: "false"
          MERGE_MSG: "Branch was auto-updated"
          RETRY_COUNT: "1"
          MERGE_CONFLICT_ACTION: "ignore"