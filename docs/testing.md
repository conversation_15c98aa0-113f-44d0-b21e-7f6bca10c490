# Testing Framework

The application uses Jest as the testing framework with Supertest for HTTP
testing. Tests are organized by type (unit, integration) and follow consistent
patterns for database handling, mocking, and assertions.

## Testing Setup

### Jest Configuration

```javascript
// jest.config.js
module.exports = {
  // Test environment
  testEnvironment: 'node',

  // Test file patterns
  testMatch: ['**/__tests__/**/*.js', '**/?(*.)+(spec|test).js'],

  // Coverage configuration
  collectCoverage: false,
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/server.js',
    '!src/config/**',
    '!src/migrations/**',
    '!src/seeders/**',
    '!src/models/index.js',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],

  // Global setup and teardown
  globalSetup: '<rootDir>/tests/globalSetup.js',
  globalTeardown: '<rootDir>/tests/globalTeardown.js',

  // Module paths
  roots: ['<rootDir>/src', '<rootDir>/tests'],

  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,

  // Timeout for tests
  testTimeout: 30000,

  // Force tests to run serially to avoid database conflicts
  maxWorkers: 1,

  // Jest-junit configuration
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: './test-results',
        outputName: 'junit.xml',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: true,
      },
    ],
  ],

  // Module name mapping for absolute imports
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },

  // Verbose output
  verbose: true,
};
```

### Test Setup File

```javascript
// tests/setup.js
require('dotenv').config({ path: '.env.test', quiet: true });

// Set test environment
process.env.NODE_ENV = 'test';

const { sequelize } = require('../src/config/sequelize');

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Global test timeout
jest.setTimeout(30000);

// Clean up after each test
afterEach(async () => {
  jest.clearAllMocks();
});

beforeAll(async () => {
  // Ensure database connection is available
  try {
    await sequelize.authenticate();
  } catch (error) {
    console.error('Failed to connect to test database:', error);
    throw error;
  }
});

afterAll(async () => {
  // Close database connection after all tests
  try {
    await sequelize.close();
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
});
```

### Test Environment Configuration

```bash
# .env.test
NODE_ENV=test

# Server Configuration
PORT=3001
HOST=localhost

# JWT Configuration
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=1h

# Database Configuration (Test Database)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=paragon_api_test
DB_USER=postgres
DB_PASSWORD=postgres

# Bcrypt Configuration (faster for tests)
SALT_ROUNDS=4

# Rate Limiting Configuration (Relaxed for testing)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_AUTH_WINDOW_MS=60000
RATE_LIMIT_AUTH_MAX_REQUESTS=50
```

## Test Types and Organization

### Directory Structure

```
tests/
├── setup.js              # Global test setup
├── controllers/           # Controller integration tests
│   ├── AuthController.test.js
│   └── UserController.test.js
├── services/              # Service unit tests
│   ├── AuthService.test.js
│   └── UserService.test.js
├── models/                # Model unit tests
│   ├── User.test.js
│   └── Product.test.js
├── inputs/                # Input validation tests
│   ├── LoginInput.test.js
│   └── CreateUserInput.test.js
├── outputs/               # Output formatting tests
│   ├── UserOutput.test.js
│   └── AuthOutput.test.js
├── middlewares/           # Middleware tests
│   ├── auth.test.js
│   └── rateLimiter.test.js
├── integration/           # End-to-end integration tests
│   ├── auth.test.js
│   └── userFlow.test.js
└── helpers/               # Test helper functions
    ├── testHelpers.js
    └── fixtures.js
```

## Database Testing Patterns

### Database Setup and Cleanup

```javascript
// tests/controllers/UserController.test.js
const request = require('supertest');
const app = require('../../src/app');
const { sequelize } = require('../../src/config/sequelize');
const { User } = require('../../src/models');

describe('UserController', () => {
  // Database setup and cleanup
  beforeAll(async () => {
    // Ensure database connection
    await sequelize.authenticate();
    // Sync database schema for tests (recreate tables)
    await sequelize.sync({ force: true });
  });

  afterAll(async () => {
    // Close database connection
    await sequelize.close();
  });

  beforeEach(async () => {
    // Clean database before each test
    await User.destroy({ where: {}, truncate: true });
  });

  // Tests here...
});
```

### Test Data Creation

```javascript
// tests/helpers/fixtures.js
const { User, Product } = require('../../src/models');

const createTestUser = async (overrides = {}) => {
  const defaultData = {
    name: 'Test User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
  };

  return User.create({ ...defaultData, ...overrides });
};

const createTestProduct = async (overrides = {}) => {
  const defaultData = {
    name: 'Test Product',
    price: 99.99,
    category: 'electronics',
    sku: 'TEST001',
    stockQuantity: 10,
  };

  return Product.create({ ...defaultData, ...overrides });
};

const createTestAdmin = async (overrides = {}) => {
  return createTestUser({
    role: 'admin',
    email: '<EMAIL>',
    ...overrides,
  });
};

module.exports = {
  createTestUser,
  createTestProduct,
  createTestAdmin,
};
```

## Controller Testing (Integration Tests)

### Basic Controller Test Pattern

```javascript
// tests/controllers/UserController.test.js
const request = require('supertest');
const app = require('../../src/app');
const { sequelize } = require('../../src/config/sequelize');
const { User } = require('../../src/models');
const { createTestUser, createTestAdmin } = require('../helpers/fixtures');
const jwt = require('jsonwebtoken');

describe('UserController', () => {
  let testUser, adminUser, userToken, adminToken;

  beforeAll(async () => {
    await sequelize.authenticate();
    await sequelize.sync({ force: true });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  beforeEach(async () => {
    // Clean database
    await User.destroy({ where: {}, truncate: true });

    // Create test users
    testUser = await createTestUser();
    adminUser = await createTestAdmin();

    // Generate tokens
    userToken = jwt.sign({ userId: testUser.id }, process.env.JWT_SECRET);
    adminToken = jwt.sign({ userId: adminUser.id }, process.env.JWT_SECRET);
  });

  describe('GET /api/v1/users/:id', () => {
    it('should return user data for valid ID', async () => {
      const response = await request(app)
        .get(`/api/v1/users/${testUser.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toMatchObject({
        id: testUser.id,
        name: testUser.name,
        email: testUser.email,
        role: testUser.role,
      });
      expect(response.body.data).not.toHaveProperty('password');
    });

    it('should return 404 for non-existent user', async () => {
      const response = await request(app)
        .get('/api/v1/users/999')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('User not found');
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app).get(`/api/v1/users/${testUser.id}`);

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('POST /api/v1/users', () => {
    it('should create user with valid data', async () => {
      const userData = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'password123',
      };

      const response = await request(app).post('/api/v1/users').send(userData);

      expect(response.status).toBe(201);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.email).toBe(userData.email);
      expect(response.body.data).not.toHaveProperty('password');

      // Verify user was created in database
      const createdUser = await User.findByPk(response.body.data.id);
      expect(createdUser).toBeTruthy();
      expect(createdUser.email).toBe(userData.email);
    });

    it('should return 400 for invalid data', async () => {
      const invalidData = {
        name: '',
        email: 'invalid-email',
        password: '123', // Too short
      };

      const response = await request(app)
        .post('/api/v1/users')
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details).toBeInstanceOf(Array);
    });

    it('should return 400 for duplicate email', async () => {
      const userData = {
        name: 'Another User',
        email: testUser.email, // Duplicate email
        password: 'password123',
      };

      const response = await request(app).post('/api/v1/users').send(userData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('already exists');
    });
  });

  describe('PUT /api/v1/users/:id', () => {
    it('should update user data', async () => {
      const updateData = {
        name: 'Updated Name',
        email: '<EMAIL>',
      };

      const response = await request(app)
        .put(`/api/v1/users/${testUser.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.data.name).toBe(updateData.name);
      expect(response.body.data.email).toBe(updateData.email);

      // Verify update in database
      await testUser.reload();
      expect(testUser.name).toBe(updateData.name);
      expect(testUser.email).toBe(updateData.email);
    });

    it('should return 403 when updating other user', async () => {
      const otherUser = await createTestUser({ email: '<EMAIL>' });

      const response = await request(app)
        .put(`/api/v1/users/${otherUser.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({ name: 'Hacked Name' });

      expect(response.status).toBe(403);
    });
  });

  describe('DELETE /api/v1/users/:id', () => {
    it('should delete user as admin', async () => {
      const response = await request(app)
        .delete(`/api/v1/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(204);

      // Verify user was deleted
      const deletedUser = await User.findByPk(testUser.id);
      expect(deletedUser).toBeNull();
    });

    it('should return 403 for non-admin user', async () => {
      const response = await request(app)
        .delete(`/api/v1/users/${testUser.id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
    });
  });
});
```

## Service Testing (Unit Tests)

### Service Test Pattern

```javascript
// tests/services/UserService.test.js
const UserService = require('../../src/services/UserService');
const { User } = require('../../src/models');
const { sequelize } = require('../../src/config/sequelize');
const InvalidError = require('../../src/errors/InvalidError');
const NotFoundError = require('../../src/errors/NotFoundError');
const { createTestUser } = require('../helpers/fixtures');

describe('UserService', () => {
  let service;

  beforeAll(async () => {
    await sequelize.sync({ force: true });
    service = new UserService();
  });

  afterAll(async () => {
    await sequelize.close();
  });

  beforeEach(async () => {
    await User.destroy({ where: {}, truncate: true });
  });

  describe('create', () => {
    it('should create user with valid data', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      };

      const user = await service.create(userData);

      expect(user.id).toBeDefined();
      expect(user.name).toBe(userData.name);
      expect(user.email).toBe(userData.email);
      expect(user.password).toBeUndefined(); // Should not return password
      expect(user.password_digest).toBeDefined(); // Should have hashed password
    });

    it('should throw InvalidError for duplicate email', async () => {
      await createTestUser({ email: '<EMAIL>' });

      const userData = {
        name: 'Another User',
        email: '<EMAIL>',
        password: 'password123',
      };

      await expect(service.create(userData)).rejects.toThrow(InvalidError);
      await expect(service.create(userData)).rejects.toThrow(
        'Email already exists',
      );
    });

    it('should hash password before saving', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      };

      const user = await service.create(userData);

      expect(user.password_digest).toBeDefined();
      expect(user.password_digest).not.toBe(userData.password);
      expect(user.password_digest.length).toBeGreaterThan(50); // Bcrypt hash length
    });
  });

  describe('findById', () => {
    it('should return user when found', async () => {
      const testUser = await createTestUser();

      const user = await service.findById(testUser.id);

      expect(user.id).toBe(testUser.id);
      expect(user.name).toBe(testUser.name);
      expect(user.email).toBe(testUser.email);
    });

    it('should throw NotFoundError when user not found', async () => {
      await expect(service.findById(999)).rejects.toThrow(NotFoundError);
      await expect(service.findById(999)).rejects.toThrow('User not found');
    });
  });

  describe('update', () => {
    it('should update user data', async () => {
      const testUser = await createTestUser();
      const updateData = {
        name: 'Updated Name',
        email: '<EMAIL>',
      };

      const updatedUser = await service.update(testUser.id, updateData);

      expect(updatedUser.name).toBe(updateData.name);
      expect(updatedUser.email).toBe(updateData.email);
      expect(updatedUser.updated_at).not.toBe(testUser.updated_at);
    });

    it('should throw NotFoundError for non-existent user', async () => {
      const updateData = { name: 'Updated Name' };

      await expect(service.update(999, updateData)).rejects.toThrow(
        NotFoundError,
      );
    });
  });

  describe('delete', () => {
    it('should delete user', async () => {
      const testUser = await createTestUser();

      const result = await service.delete(testUser.id);

      expect(result).toBe(true);

      // Verify user was deleted
      const deletedUser = await User.findByPk(testUser.id);
      expect(deletedUser).toBeNull();
    });

    it('should throw NotFoundError for non-existent user', async () => {
      await expect(service.delete(999)).rejects.toThrow(NotFoundError);
    });
  });

  describe('findAll', () => {
    it('should return paginated users', async () => {
      // Create multiple test users
      await Promise.all([
        createTestUser({ email: '<EMAIL>', name: 'User 1' }),
        createTestUser({ email: '<EMAIL>', name: 'User 2' }),
        createTestUser({ email: '<EMAIL>', name: 'User 3' }),
      ]);

      const result = await service.findAll({ page: 1, limit: 2 });

      expect(result.users).toHaveLength(2);
      expect(result.pagination).toMatchObject({
        page: 1,
        limit: 2,
        total: 3,
        totalPages: 2,
      });
    });

    it('should filter users by search term', async () => {
      await Promise.all([
        createTestUser({ email: '<EMAIL>', name: 'John Doe' }),
        createTestUser({ email: '<EMAIL>', name: 'Jane Smith' }),
        createTestUser({ email: '<EMAIL>', name: 'Bob Johnson' }),
      ]);

      const result = await service.findAll({ search: 'john' });

      expect(result.users).toHaveLength(2); // John Doe and Bob Johnson
      expect(
        result.users.every(
          user =>
            user.name.toLowerCase().includes('john') ||
            user.email.toLowerCase().includes('john'),
        ),
      ).toBe(true);
    });
  });
});
```

## Model Testing

### Model Test Pattern

```javascript
// tests/models/User.test.js
const { User } = require('../../src/models');
const { sequelize } = require('../../src/config/sequelize');

describe('User Model', () => {
  beforeAll(async () => {
    await sequelize.sync({ force: true });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  beforeEach(async () => {
    await User.destroy({ where: {}, truncate: true });
  });

  describe('validation', () => {
    it('should create user with valid data', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      };

      const user = await User.create(userData);

      expect(user.id).toBeDefined();
      expect(user.name).toBe(userData.name);
      expect(user.email).toBe(userData.email);
      expect(user.created_at).toBeDefined();
      expect(user.updated_at).toBeDefined();
    });

    it('should fail validation for invalid email', async () => {
      const userData = {
        name: 'Test User',
        email: 'invalid-email',
        password: 'password123',
      };

      await expect(User.create(userData)).rejects.toThrow();
    });

    it('should fail validation for duplicate email', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      };

      await User.create(userData);
      await expect(User.create(userData)).rejects.toThrow();
    });
  });

  describe('hooks', () => {
    it('should hash password before create', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      };

      const user = await User.create(userData);

      expect(user.password_digest).toBeDefined();
      expect(user.password_digest).not.toBe(userData.password);
    });

    it('should update timestamps on update', async () => {
      const user = await User.create({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      });

      const originalUpdatedAt = user.updated_at;

      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 10));

      await user.update({ name: 'Updated Name' });

      expect(user.updated_at).not.toEqual(originalUpdatedAt);
    });
  });

  describe('instance methods', () => {
    it('should verify correct password', async () => {
      const password = 'password123';
      const user = await User.create({
        name: 'Test User',
        email: '<EMAIL>',
        password,
      });

      const isValid = await user.verifyPassword(password);
      expect(isValid).toBe(true);
    });

    it('should reject incorrect password', async () => {
      const user = await User.create({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      });

      const isValid = await user.verifyPassword('wrongpassword');
      expect(isValid).toBe(false);
    });
  });
});
```

## Input Validation Testing

```javascript
// tests/inputs/CreateUserInput.test.js
const CreateUserInput = require('../../src/inputs/CreateUserInput');
const InvalidError = require('../../src/errors/InvalidError');

describe('CreateUserInput', () => {
  describe('valid input', () => {
    it('should validate correct data', () => {
      const validData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      };

      const input = new CreateUserInput(validData);
      expect(() => input.validate()).not.toThrow();

      const output = input.output();
      expect(output.name).toBe(validData.name);
      expect(output.email).toBe(validData.email.toLowerCase());
    });

    it('should apply defaults', () => {
      const validData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      };

      const input = new CreateUserInput(validData);
      input.validate();

      const output = input.output();
      expect(output.role).toBe('user'); // Default value
    });
  });

  describe('invalid input', () => {
    it('should throw InvalidError for missing required fields', () => {
      const invalidData = { name: 'Test' }; // Missing email and password

      const input = new CreateUserInput(invalidData);
      expect(() => input.validate()).toThrow(InvalidError);
    });

    it('should throw InvalidError for invalid email format', () => {
      const invalidData = {
        name: 'Test User',
        email: 'invalid-email',
        password: 'password123',
      };

      const input = new CreateUserInput(invalidData);
      expect(() => input.validate()).toThrow(InvalidError);
    });

    it('should throw InvalidError for short password', () => {
      const invalidData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: '123', // Too short
      };

      const input = new CreateUserInput(invalidData);
      expect(() => input.validate()).toThrow(InvalidError);
    });
  });

  describe('data transformation', () => {
    it('should normalize email to lowercase', () => {
      const data = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      };

      const input = new CreateUserInput(data);
      input.validate();

      const output = input.output();
      expect(output.email).toBe('<EMAIL>');
    });

    it('should trim whitespace from name', () => {
      const data = {
        name: '  Test User  ',
        email: '<EMAIL>',
        password: 'password123',
      };

      const input = new CreateUserInput(data);
      input.validate();

      const output = input.output();
      expect(output.name).toBe('Test User');
    });
  });
});
```

## Running Tests

### NPM Scripts

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --reporters=default --reporters=jest-junit",
    "test:setup": "node scripts/setup-test-db.js"
  }
}
```

### Test Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- tests/services/UserService.test.js

# Run tests matching pattern
npm test -- --testNamePattern="should create user"

# Run tests for specific directory
npm test -- tests/controllers/

# Run tests in CI mode
npm run test:ci
```

## Best Practices

### 1. Use Descriptive Test Names

```javascript
// ✅ Good - descriptive test names
describe('UserService', () => {
  describe('create', () => {
    it('should create user with valid data', () => {});
    it('should throw InvalidError for duplicate email', () => {});
    it('should hash password before saving', () => {});
  });
});

// ❌ Bad - vague test names
describe('UserService', () => {
  it('works', () => {});
  it('fails', () => {});
});
```

### 2. Follow AAA Pattern

```javascript
// ✅ Good - Arrange, Act, Assert
it('should create user with valid data', async () => {
  // Arrange
  const userData = {
    name: 'Test User',
    email: '<EMAIL>',
    password: 'password123',
  };

  // Act
  const user = await service.create(userData);

  // Assert
  expect(user.id).toBeDefined();
  expect(user.name).toBe(userData.name);
});
```

### 3. Clean Up Between Tests

```javascript
// ✅ Good - clean database between tests
beforeEach(async () => {
  await User.destroy({ where: {}, truncate: true });
});

// ✅ Good - clear mocks
afterEach(() => {
  jest.clearAllMocks();
});
```

### 4. Use Test Helpers and Fixtures

```javascript
// ✅ Good - reusable test data
const { createTestUser } = require('../helpers/fixtures');

it('should update user', async () => {
  const user = await createTestUser();
  // Test logic
});

// ❌ Bad - duplicate test data creation
it('should update user', async () => {
  const user = await User.create({
    name: 'Test User',
    email: '<EMAIL>',
    password: 'password123',
  });
  // Test logic
});
```

### 5. Test Error Cases

```javascript
// ✅ Good - test both success and error cases
describe('findById', () => {
  it('should return user when found', async () => {
    // Success case
  });

  it('should throw NotFoundError when user not found', async () => {
    // Error case
    await expect(service.findById(999)).rejects.toThrow(NotFoundError);
  });
});
```

### 6. Mock External Dependencies

```javascript
// ✅ Good - mock external services
jest.mock('../../src/services/EmailService');
const EmailService = require('../../src/services/EmailService');

beforeEach(() => {
  EmailService.mockClear();
});

it('should send welcome email', async () => {
  await service.create(userData);
  expect(EmailService.prototype.sendWelcomeEmail).toHaveBeenCalled();
});
```
