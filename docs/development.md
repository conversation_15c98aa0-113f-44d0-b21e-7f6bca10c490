# Development Workflow

This guide covers the complete development workflow including project setup,
environment configuration, database management, code quality tools, and
deployment processes.

## Project Setup

### Prerequisites

- **Node.js**: Version 18+ (LTS recommended)
- **PostgreSQL**: Version 12+
- **npm**: Version 8+ (comes with Node.js)
- **Git**: For version control

### Initial Setup

```bash
# Clone the repository
git clone <repository-url>
cd paragon-api

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

# Edit environment variables
nano .env  # or your preferred editor

# Set up the database
npm run db:create
npm run db:migrate
npm run db:seed  # Optional: seed with sample data

# Run tests to verify setup
npm test

# Start development server
npm run dev
```

## Environment Configuration

### Environment Files

The project uses different environment files for different stages:

- `.env` - Development environment (not committed)
- `.env.example` - Template with all required variables
- `.env.test` - Test environment configuration
- `.env.production` - Production environment (server only)

### Required Environment Variables

```bash
# .env
# Server Configuration
PORT=3000
HOST=0.0.0.0

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRES_IN=1h

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=paragon_api_dev
DB_USER=postgres
DB_PASSWORD=your_db_password

# Bcrypt Configuration
SALT_ROUNDS=12

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000          # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100          # 100 requests per window
RATE_LIMIT_SKIP_SUCCESSFUL=false
RATE_LIMIT_SKIP_FAILED=false
RATE_LIMIT_SKIP_PATHS=

# Authentication Rate Limiting
RATE_LIMIT_AUTH_WINDOW_MS=900000     # 15 minutes
RATE_LIMIT_AUTH_MAX_REQUESTS=5       # 5 attempts per window

# Public Endpoints Rate Limiting
RATE_LIMIT_PUBLIC_WINDOW_MS=900000   # 15 minutes
RATE_LIMIT_PUBLIC_MAX_REQUESTS=200   # 200 requests per window
```

### Environment-Specific Configuration

```javascript
// src/config/config.js
require('dotenv').config();

module.exports = {
  port: process.env.PORT || 3000,
  host: process.env.HOST || '0.0.0.0',
  jwtSecret: process.env.JWT_SECRET,
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1h',
  saltRounds: parseInt(process.env.SALT_ROUNDS, 10) || 12,

  // Database configuration is loaded from database.json
  // Rate limiting configuration
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000,
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100,
    // ... other rate limiting config
  },
};
```

## Database Management

### Database Configuration

```json
// src/config/database.json
{
  "development": {
    "username": "postgres",
    "password": "password",
    "database": "paragon_api_dev",
    "host": "localhost",
    "port": 5432,
    "dialect": "postgres",
    "logging": console.log
  },
  "test": {
    "username": "postgres",
    "password": "password",
    "database": "paragon_api_test",
    "host": "localhost",
    "port": 5432,
    "dialect": "postgres",
    "logging": false
  },
  "production": {
    "username": "postgres",
    "password": "password",
    "database": "paragon_api_prod",
    "host": "localhost",
    "port": 5432,
    "dialect": "postgres",
    "logging": false,
    "pool": {
      "max": 20,
      "min": 5,
      "acquire": 30000,
      "idle": 10000
    }
  }
}
```

### Migration Commands

```bash
# Create a new migration
npx sequelize-cli migration:generate --name create-users

# Run pending migrations
npm run db:migrate

# Undo last migration
npm run db:migrate:undo

# Undo all migrations
npm run db:migrate:undo:all

# Check migration status
npx sequelize-cli db:migrate:status
```

### Seeder Commands

```bash
# Create a new seeder
npx sequelize-cli seed:generate --name demo-users

# Run all seeders
npm run db:seed

# Run specific seeder
npx sequelize-cli db:seed --seed 20231201000000-demo-users.js

# Undo all seeders
npm run db:seed:undo:all

# Undo specific seeder
npx sequelize-cli db:seed:undo --seed 20231201000000-demo-users.js
```

### Database Setup Scripts

```javascript
// scripts/setup-db.js
const { sequelize } = require('../src/config/sequelize');

async function setupDatabase() {
  try {
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Sync models (development only)
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true });
      console.log('✅ Database models synchronized');
    }

    console.log('✅ Database setup complete');
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

setupDatabase();
```

## Code Quality Tools

### ESLint Configuration

```javascript
// eslint.config.js
const js = require('@eslint/js');
const globals = require('globals');
const prettier = require('eslint-plugin-prettier');
const prettierConfig = require('eslint-config-prettier');

module.exports = [
  {
    files: ['**/*.{js,mjs,cjs}'],
    plugins: { prettier },
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.es2022,
        ...globals.jest,
      },
      ecmaVersion: 2022,
      sourceType: 'commonjs',
    },
    rules: {
      ...js.configs.recommended.rules,
      ...prettierConfig.rules,
      'prettier/prettier': 'error',
      'no-console': 'off',
      'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
      'prefer-const': 'error',
      'no-var': 'error',
      'object-shorthand': 'error',
      'prefer-template': 'error',
    },
  },
];
```

### Prettier Configuration

```javascript
// .prettierrc.js
module.exports = {
  semi: true,
  trailingComma: 'all',
  singleQuote: true,
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  arrowParens: 'avoid',
  bracketSpacing: true,
  bracketSameLine: false,

  overrides: [
    {
      files: '*.json',
      options: { printWidth: 80 },
    },
    {
      files: '*.md',
      options: { printWidth: 80, proseWrap: 'always' },
    },
  ],
};
```

### Husky Git Hooks

```json
// package.json
{
  "scripts": {
    "prepare": "husky"
  },
  "lint-staged": {
    "*.{js,jsx}": ["eslint --fix", "prettier --write"],
    "*.{json,md}": ["prettier --write"]
  }
}
```

```bash
# .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx lint-staged
```

### Code Quality Commands

```bash
# Linting
npm run lint              # Check for linting errors
npm run lint:fix          # Fix auto-fixable linting errors
npm run lint:check        # Check with zero warnings tolerance

# Formatting
npm run format            # Format all files
npm run format:check      # Check if files are formatted

# Combined quality check
npm run quality:check     # Run linting and formatting checks
```

## Development Scripts

### Package.json Scripts

```json
{
  "scripts": {
    // Server
    "start": "node src/server.js",
    "dev": "nodemon src/server.js",

    // Testing
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --reporters=default --reporters=jest-junit",

    // Database
    "db:create": "sequelize-cli db:create",
    "db:drop": "sequelize-cli db:drop",
    "db:migrate": "sequelize-cli db:migrate",
    "db:migrate:undo": "sequelize-cli db:migrate:undo",
    "db:seed": "sequelize-cli db:seed:all",
    "db:seed:undo": "sequelize-cli db:seed:undo:all",
    "db:reset": "npm run db:drop && npm run db:create && npm run db:migrate && npm run db:seed",

    // Code Quality
    "lint": "eslint .",
    "lint:fix": "eslint . --fix",
    "lint:check": "eslint . --max-warnings 0",
    "format": "prettier --write .",
    "format:check": "prettier --check .",

    // Git hooks
    "prepare": "husky",

    // Utilities
    "clean": "rm -rf node_modules coverage test-results",
    "setup": "npm install && npm run db:create && npm run db:migrate"
  }
}
```

### Development Workflow Commands

```bash
# Daily development workflow
npm run dev                    # Start development server
npm run test:watch            # Run tests in watch mode (separate terminal)

# Before committing
npm run lint:fix              # Fix linting issues
npm run format                # Format code
npm test                      # Run all tests
git add .
git commit -m "feat: add new feature"

# Database changes
npx sequelize-cli migration:generate --name add-user-avatar
# Edit migration file
npm run db:migrate            # Apply migration
npm test                      # Verify tests still pass

# Reset database (when needed)
npm run db:reset              # Drop, create, migrate, and seed
```

## Development Best Practices

### Git Workflow

```bash
# Feature development
git checkout -b feature/user-authentication
# Make changes
git add .
git commit -m "feat: implement user authentication"
git push origin feature/user-authentication
# Create pull request

# Hotfix workflow
git checkout -b hotfix/fix-login-bug
# Make changes
git add .
git commit -m "fix: resolve login validation issue"
git push origin hotfix/fix-login-bug
# Create pull request
```

### Commit Message Convention

```bash
# Format: type(scope): description

# Types:
feat:     # New feature
fix:      # Bug fix
docs:     # Documentation changes
style:    # Code style changes (formatting, etc.)
refactor: # Code refactoring
test:     # Adding or updating tests
chore:    # Maintenance tasks

# Examples:
feat(auth): add JWT token refresh functionality
fix(users): resolve email validation bug
docs(api): update authentication endpoint documentation
test(services): add unit tests for UserService
refactor(controllers): extract common validation logic
chore(deps): update dependencies to latest versions
```

### Code Review Checklist

- [ ] Code follows project conventions and style guide
- [ ] All tests pass and coverage is maintained
- [ ] No console.log statements in production code
- [ ] Error handling is appropriate and consistent
- [ ] Database migrations are reversible
- [ ] Environment variables are documented
- [ ] API endpoints follow RESTful conventions
- [ ] Input validation is comprehensive
- [ ] Security best practices are followed
- [ ] Performance considerations are addressed

## Debugging

### Development Debugging

```javascript
// Use debugger statements
debugger;

// Use console.log strategically
console.log('Debug info:', { userId, requestData });

// Use VS Code debugger
// Add to .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Server",
      "program": "${workspaceFolder}/src/server.js",
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal"
    }
  ]
}
```

### Database Debugging

```javascript
// Enable SQL logging in development
// src/config/database.json
{
  "development": {
    "logging": console.log  // Shows all SQL queries
  }
}

// Debug specific queries
const users = await User.findAll({
  logging: (sql, timing) => {
    console.log('SQL:', sql);
    console.log('Timing:', timing);
  }
});
```

### API Testing with curl

```bash
# Test authentication
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Test protected endpoint
curl -X GET http://localhost:3000/api/v1/users/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test with verbose output
curl -v -X POST http://localhost:3000/api/v1/users \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"password123"}'
```

## Deployment Preparation

### Production Environment Setup

```bash
# Production environment variables
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# Use strong JWT secret
JWT_SECRET=your_very_long_and_random_production_jwt_secret_here
JWT_EXPIRES_IN=1h

# Production database
DB_HOST=your_production_db_host
DB_PORT=5432
DB_NAME=paragon_api_prod
DB_USER=your_production_db_user
DB_PASSWORD=your_production_db_password

# Higher bcrypt rounds for production
SALT_ROUNDS=12

# Stricter CORS
CORS_ORIGIN=https://yourdomain.com

# Production rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### Pre-deployment Checklist

- [ ] All tests pass (`npm test`)
- [ ] Code quality checks pass (`npm run lint:check`, `npm run format:check`)
- [ ] Environment variables are configured
- [ ] Database migrations are ready
- [ ] Security headers are configured
- [ ] Rate limiting is properly configured
- [ ] Logging is set up for production
- [ ] Health check endpoint works
- [ ] Error handling doesn't expose sensitive information
- [ ] Dependencies are up to date and secure

### Build and Deploy Commands

```bash
# Install production dependencies only
npm ci --only=production

# Run database migrations
npm run db:migrate

# Start production server
npm start

# Or with PM2 (recommended)
pm2 start src/server.js --name "paragon-api"
pm2 save
pm2 startup
```
