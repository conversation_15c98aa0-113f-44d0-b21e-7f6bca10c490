# Repositories

The repository layer provides a clean abstraction for read-only data access
operations. Repositories handle complex querying logic including pagination,
sorting, and filtering, while keeping controllers focused on HTTP concerns.

## Architecture Overview

```
Controller → Repository → Model → Database
```

- **Controllers**: Handle HTTP requests/responses and validation
- **Repositories**: Handle complex read queries with filtering, sorting,
  pagination
- **Models**: Define data structure and basic CRUD operations
- **Database**: Data persistence layer

## BaseRepository Class

The `BaseRepository` class provides common functionality for all repositories:

### Key Features

- **Automatic Pagination**: Default page=1, limit=10
- **Dynamic Sorting**: Sort by any valid model column
- **Dynamic Filtering**: Auto-discovery of `filterBy*` methods
- **Default Scoping**: Child classes define base query conditions
- **Query Parameter Mapping**: Automatic mapping from HTTP query params

### Core Methods

```javascript
// Find all records with pagination, sorting, and filtering
async findAll(queryParams = {})

// Find single record with filters
async findOne(queryParams = {})

// Count records with filters
async count(filterParams = {})

// Check if record exists by ID
async exists(id)
```

## Creating a Repository

### Step 1: Extend BaseRepository

```javascript
// src/repositories/ProductsRepository.js
const { Op } = require('sequelize');
const BaseRepository = require('./BaseRepository');
const { Product } = require('../models');

class ProductsRepository extends BaseRepository {
  constructor() {
    super(Product); // Pass the model to parent constructor
  }

  // Note: BaseRepository does not use defaultScope() method
  // All filtering is handled through filterBy* methods only

  // Filter methods are automatically discovered and applied
  filterByCategory(category) {
    if (!category) return null;
    return { category };
  }

  filterByPriceRange(priceRange) {
    if (!priceRange) return null;
    const [min, max] = priceRange.split('-').map(Number);

    const condition = {};
    if (min) condition[Op.gte] = min;
    if (max) condition[Op.lte] = max;

    return { price: condition };
  }

  filterBySearch(search) {
    if (!search) return null;
    return {
      [Op.or]: [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
      ],
    };
  }
}

module.exports = ProductsRepository;
```

### Step 2: Use in Controller

```javascript
// src/controllers/ProductsController.js
const ApiController = require('./ApiController');
const ProductsRepository = require('../repositories/ProductsRepository');
const ProductOutput = require('../outputs/ProductOutput');

class ProductsController extends ApiController {
  constructor() {
    super();
    this.repository = new ProductsRepository();
  }

  /**
   * GET /api/v1/products
   * Supports query parameters:
   * - page, limit: Pagination
   * - sort, sort_direction: Sorting
   * - category: Filter by category
   * - price_range: Filter by price range (e.g., "10-50")
   * - search: Search in name and description
   */
  index = this.createMethod(async (req, res) => {
    const result = await this.repository.findAll(req.query);

    const output = new ProductOutput(result.rows, {
      pagination: result.pagination,
    });

    output.renderJsonArray(res);
  });
}

module.exports = new ProductsController();
```

## Query Parameters

### Pagination

```
GET /api/v1/users?page=2&limit=20
```

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

### Sorting

```
GET /api/v1/users?sort=name&sort_direction=desc
```

- `sort`: Column to sort by (must be valid model attribute)
- `sort_direction`: Sort direction (`asc` or `desc`, default: `asc`)

### Filtering

Filters are automatically applied based on `filterBy*` methods:

```
GET /api/v1/users?role=admin&name=john&search=developer
```

Each query parameter is mapped to a corresponding filter method:

- `role=admin` → `filterByRole('admin')`
- `name=john` → `filterByName('john')`
- `search=developer` → `filterBySearch('developer')`

## Filter Method Patterns

### Simple Equality Filter

```javascript
filterByRole(role) {
  if (!role) return null;
  return { role: role.toLowerCase() };
}
```

### Partial Match Filter

```javascript
filterByName(name) {
  if (!name) return null;
  return {
    name: { [Op.iLike]: `%${name}%` }
  };
}
```

### Range Filter

```javascript
filterByPriceRange(range) {
  if (!range) return null;
  const [min, max] = range.split('-').map(Number);

  const condition = {};
  if (min) condition[Op.gte] = min;
  if (max) condition[Op.lte] = max;

  return { price: condition };
}
```

### Date Filter

```javascript
filterByCreatedAfter(date) {
  if (!date) return null;
  const parsedDate = new Date(date);
  if (isNaN(parsedDate.getTime())) return null;

  return {
    created_at: { [Op.gte]: parsedDate }
  };
}
```

### Multi-Field Search

```javascript
filterBySearch(search) {
  if (!search) return null;
  return {
    [Op.or]: [
      { name: { [Op.iLike]: `%${search}%` } },
      { email: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } },
    ],
  };
}
```

## Advanced Features

### Custom Sort Columns

Override `buildOrderClause` to restrict or customize sorting:

```javascript
buildOrderClause(sort, sortDirection) {
  const allowedColumns = ['name', 'price', 'created_at', 'category'];

  if (sort && allowedColumns.includes(sort)) {
    const direction = sortDirection?.toLowerCase() === 'desc' ? 'DESC' : 'ASC';
    return [[sort, direction]];
  }

  // Default sorting
  return [['created_at', 'DESC']];
}
```

### Convenience Methods

Add domain-specific methods for common queries:

```javascript
class UsersRepository extends BaseRepository {
  // Find users by role with pagination
  async findByRole(role, options = {}) {
    return await this.findAll({ role, ...options });
  }

  // Search users
  async search(searchTerm, options = {}) {
    return await this.findAll({ search: searchTerm, ...options });
  }

  // Find recent users
  async findRecent(days = 7, options = {}) {
    const date = new Date();
    date.setDate(date.getDate() - days);

    return await this.findAll({
      created_after: date.toISOString().split('T')[0],
      ...options,
    });
  }
}
```

## Testing Repositories

### Repository Unit Tests

```javascript
// tests/repositories/ProductsRepository.test.js
const ProductsRepository = require('../../src/repositories/ProductsRepository');
const { Product } = require('../../src/models');

describe('ProductsRepository', () => {
  let repository;

  beforeEach(async () => {
    repository = new ProductsRepository();
    await Product.destroy({ where: {}, truncate: true });
  });

  describe('filterByCategory', () => {
    it('should filter products by category', async () => {
      await Product.bulkCreate([
        {
          name: 'Laptop',
          category: 'electronics',
          price: 1000,
          isActive: true,
        },
        { name: 'Book', category: 'books', price: 20, isActive: true },
      ]);

      const result = await repository.findAll({ category: 'electronics' });

      expect(result.rows).toHaveLength(1);
      expect(result.rows[0].category).toBe('electronics');
    });
  });
});
```

### Controller Integration Tests

```javascript
// tests/controllers/ProductsController.test.js
describe('GET /api/v1/products', () => {
  it('should filter and paginate products', async () => {
    const response = await request(app)
      .get('/api/v1/products?category=electronics&page=1&limit=5')
      .set('Authorization', `Bearer ${token}`);

    expect(response.status).toBe(200);
    expect(response.body.data.every(p => p.category === 'electronics')).toBe(
      true,
    );
    expect(response.body.pagination.limit).toBe(5);
  });
});
```

## Best Practices

### 1. Keep Repositories Read-Only

Repositories should only handle read operations. Use services for write
operations:

```javascript
// ✅ Good - Repository for reads
const users = await this.repository.findAll(req.query);

// ✅ Good - Service for writes
const user = await this.service.create(userData);
```

### 2. Use filterBy Methods for All Filtering

BaseRepository uses filterBy methods for all filtering logic:

```javascript
// ✅ Good - Use filterBy methods for common filters
filterByActive(isActive) {
  if (isActive === undefined) return null;
  return { isActive: isActive === 'true' };
}

// ✅ Good - Combine multiple filters as needed
filterByStatus(status) {
  if (!status) return null;
  return { isDeleted: false, status };
}
```

### 3. Validate Filter Inputs

Always validate filter inputs and return null for invalid values:

```javascript
// ✅ Good - Input validation
filterByPrice(price) {
  const numPrice = parseFloat(price);
  if (!price || isNaN(numPrice) || numPrice < 0) return null;
  return { price: numPrice };
}

// ❌ Bad - No validation
filterByPrice(price) {
  return { price }; // Could cause database errors
}
```

### 4. Use Descriptive Filter Names

Filter method names should clearly indicate what they filter:

```javascript
// ✅ Good - Clear filter names
filterByRole(role) { /* ... */ }
filterByCreatedAfter(date) { /* ... */ }
filterByPriceRange(range) { /* ... */ }

// ❌ Bad - Unclear names
filterBy1(value) { /* ... */ }
filter(params) { /* ... */ }
```

### 5. Document Query Parameters

Always document supported query parameters in controller methods:

```javascript
/**
 * GET /api/v1/products
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 10)
 * - sort_column: Column to sort by
 * - sort: Sort direction (asc/desc)
 * - category: Filter by category
 * - price_range: Price range filter (e.g., "10-50")
 * - search: Search in name and description
 */
index = this.createMethod(async (req, res) => {
  // Implementation
});
```

## Error Handling

Repositories inherit error handling from `BaseRepository`:

- Invalid models throw `InvalidError`
- Database errors are propagated to controllers
- Invalid sort columns are ignored (falls back to default)
- Invalid filter values return null (no filter applied)

## Performance Considerations

### 1. Index Database Columns

Ensure frequently filtered/sorted columns are indexed:

```sql
-- Add indexes for common filters
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_products_category ON products(category);
```

### 2. Limit Result Sets

Always use pagination to prevent large result sets:

```javascript
// ✅ Good - Reasonable limits
const maxLimit = 100;
const limit = Math.min(parseInt(queryParams.limit) || 10, maxLimit);
```

### 3. Optimize Complex Filters

For complex filters, consider database-specific optimizations:

```javascript
// Consider using database functions for complex searches
filterByFullTextSearch(search) {
  if (!search) return null;

  // PostgreSQL full-text search example
  return sequelize.where(
    sequelize.fn('to_tsvector', sequelize.col('name')),
    '@@',
    sequelize.fn('plainto_tsquery', search)
  );
}
```
