# Input Validation

The input validation system uses JSON Schema with AJV to validate and transform
incoming request data. All input classes extend the `ApplicationInput` base
class.

## ApplicationInput Base Class

The `ApplicationInput` class provides:

- **JSON Schema Validation**: Uses AJV for robust validation
- **Data Transformation**: Coerces types and applies defaults
- **Error Handling**: Throws `InvalidError` with detailed validation errors
- **Data Cleaning**: Removes additional properties and sanitizes input

### Key Features

- **Type Coercion**: Automatically converts strings to numbers, booleans, etc.
- **Format Validation**: Built-in formats like email, date, URL, etc.
- **Custom Error Messages**: Detailed validation error reporting
- **Default Values**: Applies default values from schema
- **Additional Property Removal**: Strips unknown fields for security

## Input Class Structure

### Basic Input Pattern

```javascript
const ApplicationInput = require('./ApplicationInput');

class SomeInput extends ApplicationInput {
  /**
   * Define the JSON schema for validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          minLength: 1,
          maxLength: 100,
        },
        email: {
          type: 'string',
          format: 'email',
        },
        age: {
          type: 'integer',
          minimum: 0,
          maximum: 150,
        },
      },
      required: ['name', 'email'],
      additionalProperties: false,
    };
  }

  /**
   * Optional: Transform validated data before output
   * @returns {Object} Transformed data
   */
  output() {
    const data = super.output();

    // Custom transformations
    data.email = data.email.toLowerCase().trim();
    data.name = data.name.trim();

    return data;
  }
}

module.exports = SomeInput;
```

### Usage in Controllers

```javascript
create = this.createMethod(async (req, res) => {
  // Create input instance with request data
  const input = new CreateUserInput(req.body);

  // Validate - throws InvalidError if validation fails
  input.validate();

  // Get validated and transformed data
  const validatedData = input.output();

  // Pass to service
  const result = await this.service.create(validatedData);

  // ... rest of controller logic
});
```

## Creating a New Input Class

### Step 1: Create the Input File

Create a new file in `src/inputs/` following the naming convention
`{Action}{Entity}Input.js`:

```javascript
// src/inputs/CreateProductInput.js
const ApplicationInput = require('./ApplicationInput');

class CreateProductInput extends ApplicationInput {
  schema() {
    return {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          minLength: 1,
          maxLength: 255,
          description: 'Product name',
        },
        description: {
          type: 'string',
          maxLength: 1000,
          default: '',
        },
        price: {
          type: 'number',
          minimum: 0,
          multipleOf: 0.01, // Ensures 2 decimal places
        },
        category: {
          type: 'string',
          enum: ['electronics', 'clothing', 'books', 'home'],
        },
        tags: {
          type: 'array',
          items: {
            type: 'string',
            minLength: 1,
            maxLength: 50,
          },
          maxItems: 10,
          uniqueItems: true,
          default: [],
        },
        isActive: {
          type: 'boolean',
          default: true,
        },
      },
      required: ['name', 'price', 'category'],
      additionalProperties: false,
    };
  }

  output() {
    const data = super.output();

    // Normalize name
    data.name = data.name.trim();

    // Ensure tags are lowercase
    data.tags = data.tags.map(tag => tag.toLowerCase().trim());

    return data;
  }
}

module.exports = CreateProductInput;
```

### Step 2: Use in Controller

```javascript
// src/controllers/ProductController.js
const CreateProductInput = require('../inputs/CreateProductInput');

class ProductController extends ApiController {
  create = this.createMethod(async (req, res) => {
    const input = new CreateProductInput(req.body);
    input.validate();

    const product = await this.service.create(input.output());
    const output = new ProductOutput(product);

    output.renderJson(res);
  });
}
```

## JSON Schema Reference

### Basic Types

```javascript
// String validation
name: {
  type: 'string',
  minLength: 1,
  maxLength: 100,
  pattern: '^[a-zA-Z\\s]+$' // Regex pattern
}

// Number validation
price: {
  type: 'number', // or 'integer' for whole numbers
  minimum: 0,
  maximum: 1000,
  multipleOf: 0.01
}

// Boolean validation
isActive: {
  type: 'boolean',
  default: true
}

// Array validation
tags: {
  type: 'array',
  items: { type: 'string' },
  minItems: 1,
  maxItems: 10,
  uniqueItems: true
}

// Object validation
address: {
  type: 'object',
  properties: {
    street: { type: 'string' },
    city: { type: 'string' }
  },
  required: ['street', 'city']
}
```

### Built-in Formats

```javascript
email: {
  type: 'string',
  format: 'email'
}

website: {
  type: 'string',
  format: 'uri'
}

birthDate: {
  type: 'string',
  format: 'date' // YYYY-MM-DD
}

createdAt: {
  type: 'string',
  format: 'date-time' // ISO 8601
}
```

### Conditional Validation

```javascript
schema() {
  return {
    type: 'object',
    properties: {
      type: {
        type: 'string',
        enum: ['individual', 'business']
      },
      name: { type: 'string' },
      companyName: { type: 'string' }
    },
    required: ['type', 'name'],
    // Conditional requirements
    if: {
      properties: { type: { const: 'business' } }
    },
    then: {
      required: ['companyName']
    }
  };
}
```

### Enum Validation

```javascript
status: {
  type: 'string',
  enum: ['draft', 'published', 'archived'],
  default: 'draft'
}

priority: {
  type: 'integer',
  enum: [1, 2, 3, 4, 5],
  description: '1 = Low, 5 = High'
}
```

## Advanced Patterns

### Nested Object Validation

```javascript
// src/inputs/CreateOrderInput.js
schema() {
  return {
    type: 'object',
    properties: {
      customer: {
        type: 'object',
        properties: {
          name: { type: 'string', minLength: 1 },
          email: { type: 'string', format: 'email' }
        },
        required: ['name', 'email'],
        additionalProperties: false
      },
      items: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            productId: { type: 'integer', minimum: 1 },
            quantity: { type: 'integer', minimum: 1 },
            price: { type: 'number', minimum: 0 }
          },
          required: ['productId', 'quantity', 'price'],
          additionalProperties: false
        },
        minItems: 1
      }
    },
    required: ['customer', 'items'],
    additionalProperties: false
  };
}
```

### Custom Data Transformation

```javascript
output() {
  const data = super.output();

  // Normalize email
  if (data.email) {
    data.email = data.email.toLowerCase().trim();
  }

  // Parse phone number
  if (data.phone) {
    data.phone = data.phone.replace(/\D/g, ''); // Remove non-digits
  }

  // Convert date strings to Date objects
  if (data.birthDate) {
    data.birthDate = new Date(data.birthDate);
  }

  // Calculate derived fields
  if (data.items) {
    data.totalAmount = data.items.reduce((sum, item) =>
      sum + (item.price * item.quantity), 0
    );
  }

  return data;
}
```

### File Upload Validation

```javascript
// For multipart/form-data with files
schema() {
  return {
    type: 'object',
    properties: {
      title: { type: 'string', minLength: 1 },
      description: { type: 'string' },
      // File validation happens in middleware/controller
      // Schema validates other form fields
    },
    required: ['title'],
    additionalProperties: false
  };
}

// In controller, validate file separately
upload = this.createMethod(async (req, res) => {
  // Validate file
  if (!req.file) {
    throw new InvalidError('File is required');
  }

  if (req.file.size > 5 * 1024 * 1024) { // 5MB
    throw new InvalidError('File too large');
  }

  // Validate other form data
  const input = new UploadInput(req.body);
  input.validate();

  // ... rest of logic
});
```

## Testing Input Classes

```javascript
// tests/inputs/CreateProductInput.test.js
const CreateProductInput = require('../../src/inputs/CreateProductInput');
const InvalidError = require('../../src/errors/InvalidError');

describe('CreateProductInput', () => {
  describe('valid input', () => {
    it('should validate correct data', () => {
      const validData = {
        name: 'Test Product',
        price: 99.99,
        category: 'electronics',
      };

      const input = new CreateProductInput(validData);
      expect(() => input.validate()).not.toThrow();

      const output = input.output();
      expect(output.name).toBe('Test Product');
      expect(output.price).toBe(99.99);
    });
  });

  describe('invalid input', () => {
    it('should throw InvalidError for missing required fields', () => {
      const invalidData = { name: 'Test' }; // Missing price and category

      const input = new CreateProductInput(invalidData);
      expect(() => input.validate()).toThrow(InvalidError);
    });

    it('should throw InvalidError for invalid email format', () => {
      const invalidData = {
        name: 'Test',
        price: 99.99,
        category: 'electronics',
        contactEmail: 'invalid-email',
      };

      const input = new CreateProductInput(invalidData);
      expect(() => input.validate()).toThrow(InvalidError);
    });
  });
});
```

## Best Practices

### 1. Keep Schemas Focused

Each input class should validate one specific operation:

```javascript
// ✅ Good - specific to one operation
class CreateUserInput extends ApplicationInput { ... }
class UpdateUserInput extends ApplicationInput { ... }
class LoginInput extends ApplicationInput { ... }

// ❌ Bad - trying to handle multiple operations
class UserInput extends ApplicationInput { ... }
```

### 2. Use Descriptive Property Names

```javascript
// ✅ Good - clear property names
properties: {
  firstName: { type: 'string' },
  lastName: { type: 'string' },
  dateOfBirth: { type: 'string', format: 'date' }
}

// ❌ Bad - unclear abbreviations
properties: {
  fName: { type: 'string' },
  lName: { type: 'string' },
  dob: { type: 'string' }
}
```

### 3. Always Set additionalProperties: false

```javascript
schema() {
  return {
    type: 'object',
    properties: { ... },
    additionalProperties: false // Prevents unknown fields
  };
}
```

### 4. Provide Helpful Error Messages

```javascript
name: {
  type: 'string',
  minLength: 1,
  maxLength: 100,
  description: 'User full name (1-100 characters)'
}
```

### 5. Use Defaults Wisely

```javascript
isActive: {
  type: 'boolean',
  default: true
},
tags: {
  type: 'array',
  items: { type: 'string' },
  default: []
}
```
