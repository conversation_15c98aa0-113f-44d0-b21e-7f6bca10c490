# Services

The service layer contains the business logic of the application. Services
coordinate between controllers and models, handling complex operations, data
transformations, and business rules. All services extend the `AppService` base
class.

## AppService Base Class

The `AppService` class provides:

- **Configuration Access**: Access to application configuration
- **Common Utilities**: Helper methods for validation and error handling
- **Consistent Error Handling**: Standardized error throwing patterns
- **Base Functionality**: Common patterns used across all services

### Key Features

- **assert()**: Throws `InvalidError` if condition is false
- **exists()**: Throws `NotFoundError` if object is null/undefined
- **Config Access**: Access to application configuration via `this.config`

## Service Structure

### Basic Service Pattern

```javascript
const AppService = require('./AppService');
const { SomeModel } = require('../models');
const InvalidError = require('../errors/InvalidError');
const NotFoundError = require('../errors/NotFoundError');

class SomeService extends AppService {
  constructor() {
    super();
    // Initialize any dependencies
  }

  /**
   * Create a new resource
   * @param {Object} data - Validated input data
   * @returns {Object} Created resource
   */
  async create(data) {
    // Business logic validation
    await this.validateBusinessRules(data);

    // Create the resource
    const resource = await SomeModel.create(data);

    // Post-creation logic
    await this.afterCreate(resource);

    return resource;
  }

  /**
   * Find resource by ID
   * @param {number} id - Resource ID
   * @returns {Object} Found resource
   */
  async findById(id) {
    const resource = await SomeModel.findByPk(id);
    this.exists(resource, 'Resource not found');

    return resource;
  }

  /**
   * Update existing resource
   * @param {number} id - Resource ID
   * @param {Object} data - Update data
   * @returns {Object} Updated resource
   */
  async update(id, data) {
    const resource = await this.findById(id);

    // Business logic validation
    await this.validateUpdateRules(resource, data);

    // Update the resource
    await resource.update(data);

    return resource;
  }

  /**
   * Delete resource
   * @param {number} id - Resource ID
   * @returns {boolean} Success status
   */
  async delete(id) {
    const resource = await this.findById(id);

    // Check if deletion is allowed
    await this.validateDeletion(resource);

    await resource.destroy();
    return true;
  }

  // Private helper methods
  async validateBusinessRules(data) {
    // Implement business validation logic
  }

  async validateUpdateRules(resource, data) {
    // Implement update validation logic
  }

  async validateDeletion(resource) {
    // Implement deletion validation logic
  }

  async afterCreate(resource) {
    // Post-creation logic (send emails, create related records, etc.)
  }
}

module.exports = SomeService;
```

## Creating a New Service

### Step 1: Create the Service File

Create a new file in `src/services/` following the naming convention
`{Entity}Service.js`:

```javascript
// src/services/ProductService.js
const AppService = require('./AppService');
const { Product, Category } = require('../models');
const InvalidError = require('../errors/InvalidError');
const NotFoundError = require('../errors/NotFoundError');

class ProductService extends AppService {
  /**
   * Create a new product
   * @param {Object} data - Product data
   * @returns {Object} Created product
   */
  async create(data) {
    // Validate category exists
    if (data.categoryId) {
      const category = await Category.findByPk(data.categoryId);
      this.exists(category, 'Category not found');
    }

    // Check for duplicate SKU
    if (data.sku) {
      const existingProduct = await Product.findOne({
        where: { sku: data.sku },
      });
      this.assert(!existingProduct, 'SKU already exists');
    }

    // Create the product
    const product = await Product.create(data);

    // Load associations for response
    await product.reload({
      include: [{ model: Category, as: 'category' }],
    });

    return product;
  }

  /**
   * Find product by ID with associations
   * @param {number} id - Product ID
   * @returns {Object} Product with associations
   */
  async findById(id) {
    const product = await Product.findByPk(id, {
      include: [
        { model: Category, as: 'category' },
        { model: Review, as: 'reviews' },
      ],
    });

    this.exists(product, 'Product not found');
    return product;
  }

  /**
   * Find all products with filtering and pagination
   * @param {Object} options - Query options
   * @returns {Object} Products with pagination info
   */
  async findAll(options = {}) {
    const {
      page = 1,
      limit = 10,
      category,
      minPrice,
      maxPrice,
      search,
      isActive = true,
    } = options;

    const offset = (page - 1) * limit;
    const where = { isActive };

    // Add filters
    if (category) {
      where.category = category;
    }

    if (minPrice || maxPrice) {
      where.price = {};
      if (minPrice) where.price[Op.gte] = minPrice;
      if (maxPrice) where.price[Op.lte] = maxPrice;
    }

    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
      ];
    }

    const { rows: products, count: total } = await Product.findAndCountAll({
      where,
      include: [{ model: Category, as: 'category' }],
      order: [['name', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
    });

    return {
      products,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Update product stock
   * @param {number} id - Product ID
   * @param {number} quantity - Quantity change (can be negative)
   * @returns {Object} Updated product
   */
  async updateStock(id, quantity) {
    const product = await this.findById(id);

    const newStock = product.stockQuantity + quantity;
    this.assert(newStock >= 0, 'Insufficient stock');

    await product.update({ stockQuantity: newStock });
    return product;
  }

  /**
   * Check product availability
   * @param {number} id - Product ID
   * @param {number} quantity - Required quantity
   * @returns {boolean} Availability status
   */
  async checkAvailability(id, quantity) {
    const product = await this.findById(id);
    return product.stockQuantity >= quantity;
  }

  /**
   * Get products by category
   * @param {string} category - Category name
   * @returns {Array} Products in category
   */
  async findByCategory(category) {
    return Product.findAll({
      where: { category, isActive: true },
      include: [{ model: Category, as: 'category' }],
      order: [['name', 'ASC']],
    });
  }

  /**
   * Get low stock products
   * @param {number} threshold - Stock threshold (default: 10)
   * @returns {Array} Products with low stock
   */
  async findLowStock(threshold = 10) {
    return Product.findAll({
      where: {
        stockQuantity: { [Op.lte]: threshold },
        isActive: true,
      },
      order: [['stockQuantity', 'ASC']],
    });
  }

  /**
   * Bulk update product prices
   * @param {Array} updates - Array of {id, price} objects
   * @returns {Array} Updated products
   */
  async bulkUpdatePrices(updates) {
    const results = [];

    for (const update of updates) {
      const product = await this.findById(update.id);
      this.assert(
        update.price > 0,
        `Invalid price for product ${product.name}`,
      );

      await product.update({ price: update.price });
      results.push(product);
    }

    return results;
  }

  /**
   * Delete product (soft delete by setting isActive to false)
   * @param {number} id - Product ID
   * @returns {boolean} Success status
   */
  async delete(id) {
    const product = await this.findById(id);

    // Check if product has pending orders
    const pendingOrders = await this.checkPendingOrders(product.id);
    this.assert(!pendingOrders, 'Cannot delete product with pending orders');

    // Soft delete
    await product.update({ isActive: false });
    return true;
  }

  /**
   * Hard delete product (permanent deletion)
   * @param {number} id - Product ID
   * @returns {boolean} Success status
   */
  async hardDelete(id) {
    const product = await this.findById(id);

    // More strict validation for hard delete
    const hasOrders = await this.checkAnyOrders(product.id);
    this.assert(
      !hasOrders,
      'Cannot permanently delete product with order history',
    );

    await product.destroy();
    return true;
  }

  // Private helper methods
  async checkPendingOrders(productId) {
    const { OrderItem } = require('../models');
    const pendingItems = await OrderItem.findOne({
      where: { product_id: productId },
      include: [
        {
          model: Order,
          as: 'order',
          where: { status: ['pending', 'processing'] },
        },
      ],
    });

    return !!pendingItems;
  }

  async checkAnyOrders(productId) {
    const { OrderItem } = require('../models');
    const orderItems = await OrderItem.findOne({
      where: { product_id: productId },
    });

    return !!orderItems;
  }
}

module.exports = ProductService;
```

### Step 2: Use in Controller

```javascript
// src/controllers/ProductController.js
const ProductService = require('../services/ProductService');

class ProductController extends ApiController {
  constructor() {
    super();
    this.service = new ProductService();
  }

  create = this.createMethod(async (req, res) => {
    const input = new CreateProductInput(req.body);
    input.validate();

    const product = await this.service.create(input.output());
    const output = new ProductOutput(product, { statusCode: 201 });

    output.renderJson(res);
  });

  index = this.createMethod(async (req, res) => {
    const result = await this.service.findAll(req.query);
    const output = new ProductOutput(result.products, {
      pagination: result.pagination,
    });

    output.renderPaginatedJson(res);
  });
}
```

## Service Patterns

### Transaction Handling

```javascript
const { sequelize } = require('../config/sequelize');

class OrderService extends AppService {
  async createOrder(orderData) {
    const transaction = await sequelize.transaction();

    try {
      // Create order
      const order = await Order.create(orderData, { transaction });

      // Create order items and update stock
      for (const item of orderData.items) {
        await OrderItem.create(
          {
            order_id: order.id,
            product_id: item.productId,
            quantity: item.quantity,
            price: item.price,
          },
          { transaction },
        );

        // Update product stock
        const product = await Product.findByPk(item.productId, { transaction });
        this.assert(
          product.stockQuantity >= item.quantity,
          'Insufficient stock',
        );

        await product.update(
          {
            stockQuantity: product.stockQuantity - item.quantity,
          },
          { transaction },
        );
      }

      await transaction.commit();
      return order;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
```

### External API Integration

```javascript
const axios = require('axios');

class PaymentService extends AppService {
  constructor() {
    super();
    this.paymentGatewayUrl = this.config.paymentGatewayUrl;
    this.apiKey = this.config.paymentApiKey;
  }

  async processPayment(paymentData) {
    try {
      const response = await axios.post(
        `${this.paymentGatewayUrl}/charges`,
        {
          amount: paymentData.amount,
          currency: paymentData.currency,
          source: paymentData.token,
        },
        {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        },
      );

      return {
        success: true,
        transactionId: response.data.id,
        status: response.data.status,
      };
    } catch (error) {
      if (error.response) {
        throw new InvalidError(
          `Payment failed: ${error.response.data.message}`,
        );
      }
      throw new Error('Payment service unavailable');
    }
  }
}
```

### Caching

```javascript
const Redis = require('redis');
const redis = Redis.createClient();

class ProductService extends AppService {
  async findById(id) {
    const cacheKey = `product:${id}`;

    // Try cache first
    const cached = await redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }

    // Fetch from database
    const product = await Product.findByPk(id, {
      include: [{ model: Category, as: 'category' }],
    });

    this.exists(product, 'Product not found');

    // Cache for 1 hour
    await redis.setex(cacheKey, 3600, JSON.stringify(product));

    return product;
  }

  async update(id, data) {
    const product = await super.update(id, data);

    // Invalidate cache
    await redis.del(`product:${id}`);

    return product;
  }
}
```

### Background Jobs

```javascript
const Queue = require('bull');
const emailQueue = new Queue('email processing');

class NotificationService extends AppService {
  async sendWelcomeEmail(userId) {
    // Queue the email job instead of sending immediately
    await emailQueue.add('welcome-email', {
      userId,
      template: 'welcome',
      priority: 'high',
    });
  }

  async sendOrderConfirmation(orderId) {
    await emailQueue.add('order-confirmation', {
      orderId,
      template: 'order-confirmation',
      priority: 'normal',
    });
  }
}

// Process jobs
emailQueue.process('welcome-email', async job => {
  const { userId, template } = job.data;
  // Send email logic here
});
```

## Testing Services

```javascript
// tests/services/ProductService.test.js
const ProductService = require('../../src/services/ProductService');
const { Product, Category } = require('../../src/models');
const { sequelize } = require('../../src/config/sequelize');
const InvalidError = require('../../src/errors/InvalidError');
const NotFoundError = require('../../src/errors/NotFoundError');

describe('ProductService', () => {
  let service;

  beforeAll(async () => {
    await sequelize.sync({ force: true });
    service = new ProductService();
  });

  afterAll(async () => {
    await sequelize.close();
  });

  beforeEach(async () => {
    await Product.destroy({ where: {}, truncate: true });
    await Category.destroy({ where: {}, truncate: true });
  });

  describe('create', () => {
    it('should create a product with valid data', async () => {
      const category = await Category.create({ name: 'Electronics' });
      const productData = {
        name: 'Test Product',
        price: 99.99,
        category: 'electronics',
        sku: 'TEST001',
        categoryId: category.id,
      };

      const product = await service.create(productData);

      expect(product.id).toBeDefined();
      expect(product.name).toBe('Test Product');
      expect(product.price).toBe(99.99);
    });

    it('should throw error for duplicate SKU', async () => {
      await Product.create({
        name: 'Existing Product',
        price: 50,
        category: 'electronics',
        sku: 'TEST001',
      });

      const productData = {
        name: 'New Product',
        price: 99.99,
        category: 'electronics',
        sku: 'TEST001',
      };

      await expect(service.create(productData)).rejects.toThrow(InvalidError);
    });
  });

  describe('findById', () => {
    it('should return product when found', async () => {
      const created = await Product.create({
        name: 'Test Product',
        price: 99.99,
        category: 'electronics',
        sku: 'TEST001',
      });

      const product = await service.findById(created.id);
      expect(product.id).toBe(created.id);
    });

    it('should throw NotFoundError when product not found', async () => {
      await expect(service.findById(999)).rejects.toThrow(NotFoundError);
    });
  });

  describe('updateStock', () => {
    it('should update stock correctly', async () => {
      const product = await Product.create({
        name: 'Test Product',
        price: 99.99,
        category: 'electronics',
        sku: 'TEST001',
        stockQuantity: 10,
      });

      const updated = await service.updateStock(product.id, 5);
      expect(updated.stockQuantity).toBe(15);
    });

    it('should throw error for insufficient stock', async () => {
      const product = await Product.create({
        name: 'Test Product',
        price: 99.99,
        category: 'electronics',
        sku: 'TEST001',
        stockQuantity: 5,
      });

      await expect(service.updateStock(product.id, -10)).rejects.toThrow(
        InvalidError,
      );
    });
  });
});
```

## Best Practices

### 1. Keep Services Focused

Each service should handle one domain entity:

```javascript
// ✅ Good - focused on user operations
class UserService extends AppService {
  async create(data) { ... }
  async findById(id) { ... }
  async update(id, data) { ... }
}

// ❌ Bad - handling multiple domains
class UserOrderProductService extends AppService {
  // Too many responsibilities
}
```

### 2. Use Transactions for Multi-Step Operations

```javascript
// ✅ Good - wrapped in transaction
async createOrderWithPayment(orderData, paymentData) {
  const transaction = await sequelize.transaction();
  try {
    const order = await this.createOrder(orderData, { transaction });
    const payment = await this.processPayment(paymentData, { transaction });
    await transaction.commit();
    return { order, payment };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

### 3. Handle Errors Appropriately

```javascript
// ✅ Good - specific error handling
async findById(id) {
  const resource = await Model.findByPk(id);
  this.exists(resource, 'Resource not found'); // Throws NotFoundError
  return resource;
}

async create(data) {
  const existing = await Model.findOne({ where: { email: data.email } });
  this.assert(!existing, 'Email already exists'); // Throws InvalidError
  return Model.create(data);
}
```

### 4. Use Dependency Injection

```javascript
// ✅ Good - injectable dependencies
class OrderService extends AppService {
  constructor(paymentService = null, emailService = null) {
    super();
    this.paymentService = paymentService || new PaymentService();
    this.emailService = emailService || new EmailService();
  }
}

// Easy to test with mocks
const mockPaymentService = { processPayment: jest.fn() };
const service = new OrderService(mockPaymentService);
```

### 5. Separate Business Logic from Data Access

```javascript
// ✅ Good - business logic in service
class ProductService extends AppService {
  async applyDiscount(productId, discountPercent) {
    // Business validation
    this.assert(
      discountPercent > 0 && discountPercent <= 50,
      'Invalid discount',
    );

    const product = await this.findById(productId);

    // Business logic
    const discountAmount = product.price * (discountPercent / 100);
    const newPrice = product.price - discountAmount;

    await product.update({ price: newPrice });
    return product;
  }
}
```
