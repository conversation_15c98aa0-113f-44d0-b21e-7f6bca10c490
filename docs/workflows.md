# GitHub Workflows

This directory contains automated CI/CD workflows for the project:

- `autoupdate.yml`: Automatically updates pull request branches when their base
  branch (e.g. main) receives new commits. This helps keep PRs up-to-date and
  reduces merge conflicts.

The workflows are triggered on specific events like push, pull request, or
manual dispatch to ensure code quality and automate deployment processes.
