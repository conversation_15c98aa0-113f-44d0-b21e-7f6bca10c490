# CircleCI Staging Deployment Setup

This document provides instructions for setting up CircleCI deployment to your AWS EKS staging environment.

## Prerequisites

1. **AWS EKS Cluster**: Make sure your EKS cluster is set up and running
2. **ECR Repository**: Create an ECR repository named `paragon-api` for your Docker images
3. **RDS Database**: Set up a PostgreSQL database for staging
4. **CircleCI Account**: Access to CircleCI with your repository connected

## CircleCI Environment Variables

You need to set up the following environment variables in your CircleCI project settings:

### Required Environment Variables
```
AWS_ECR_REGISTRY_ID=************
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
CLUSTER_NAME=your-eks-cluster-name
ENV_FILE_CONTENT=NODE_ENV=staging
PORT=3000
JWT_SECRET=your-jwt-secret
# Add other environment variables as needed

DATABASE_JSON_CONTENT={
  "staging": {
    "username": "paragon_user",
    "password": "your-database-password",
    "database": "paragon_api_staging",
    "host": "your-rds-endpoint.region.rds.amazonaws.com",
    "port": "5432",
    "dialect": "postgres",
    "logging": false,
    "pool": {
      "max": 10,
      "min": 2,
      "acquire": 30000,
      "idle": 10000
    },
    "dialectOptions": {
      "ssl": {
        "require": true,
        "rejectUnauthorized": false
      }
    }
  }
}
```

## Setup Steps

### 1. Configure Environment Variables

Go to CircleCI → Project Settings → Environment Variables and add all the required environment variables listed above.

### 2. Configure AWS IAM Permissions

Your CircleCI AWS user needs the following permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:BatchGetImage",
                "ecr:GetAuthorizationToken",
                "ecr:PutImage",
                "ecr:InitiateLayerUpload",
                "ecr:UploadLayerPart",
                "ecr:CompleteLayerUpload"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "eks:DescribeCluster",
                "eks:ListClusters"
            ],
            "Resource": "*"
        }
    ]
}
```

### 3. Test the Pipeline

Push changes to any branch except `master` and release branches (those matching `/(release|hotfix|v)\/.*/`) to trigger the deployment pipeline after manual approval.

## Pipeline Steps

The CircleCI pipeline performs the following steps:

1. **Manual Approval**: Requires manual approval before deployment
2. **Start Timer**: Begins tracking deployment duration
3. **Checkout**: Checks out the repository code
4. **Set Image Tag**: Extracts git commit hash for image tagging
5. **Setup Remote Docker**: Prepares Docker environment for building
6. **Build & Push to ECR**: Creates Docker image tagged with commit hash and pushes to ECR repository `paragon-api`
7. **Configure Kubectl**: Sets up Kubernetes configuration for EKS cluster
8. **Prepare Environment Files**: Creates `.env` and `database.json` from CircleCI environment variables
9. **Run Database Migrations**: Executes `npm run db:migrate` using Docker container with mounted config files
10. **Update Deployment**: Updates Kubernetes deployment `paragon-api` with new image tag (commit hash)
11. **Annotate Deployment**: Adds metadata to deployment for tracking and rollback purposes
12. **End Timer**: Calculates and logs total deployment duration

## Troubleshooting

### Migration Issues
If database migrations fail, check the CircleCI logs for the "Migrate database" step. The migration runs in a Docker container using:
- Mounted `.env` file with environment variables
- Mounted `database.json` file with staging database configuration
- Command: `npm run db:migrate` (which runs `sequelize-cli db:migrate`)

### Deployment Issues
Check deployment status:
```bash
kubectl get deployment paragon-api
kubectl describe deployment paragon-api
kubectl get pods -l app=paragon-api
```

### Configuration Issues
Verify environment variables are properly set in CircleCI:
- Check that `ENV_FILE_CONTENT` contains valid environment variables
- Ensure `DATABASE_JSON_CONTENT` contains valid JSON with staging configuration
- Verify AWS credentials (`AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`) and ECR repository access
- Confirm `CLUSTER_NAME` matches your EKS cluster name

### Image Issues
If image push/pull fails:
- Verify `AWS_ECR_REGISTRY_ID` is correct
- Ensure ECR repository `paragon-api` exists
- Check AWS IAM permissions for ECR access

### Rollback
To rollback to a previous version:
```bash
kubectl rollout undo deployment/paragon-api
```

View rollout history:
```bash
kubectl rollout history deployment/paragon-api
```

## Security Notes

1. Store all sensitive data in CircleCI environment variables
2. Use IAM roles with minimal required permissions for ECR and EKS access
3. Regularly rotate database passwords and JWT secrets
4. Monitor ECR for security vulnerabilities
5. Ensure database connections use SSL in staging environment

## Key Features

- **Commit-based Deployment**: Each deployment is tagged with git commit hash for traceability
- **Manual Approval**: Requires approval before deployment to staging
- **Database Migrations**: Automatic migration execution before deployment
- **Rollback Support**: Easy rollback using Kubernetes rollout commands
- **Environment Isolation**: Staging-specific configuration management
- **Deployment Tracking**: Annotations and timing for deployment monitoring
