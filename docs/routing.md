# Routing and API Structure

The application follows RESTful API conventions with versioned endpoints. Routes
are organized by resource and use Express Router for modular route handling.

## API Structure

### Base URL Structure

```
https://api.example.com/api/v1/{resource}
```

- **Base Path**: `/api/v1/`
- **Versioning**: Version number in URL path
- **Resources**: Plural noun endpoints (users, products, orders)

### HTTP Methods and Conventions

| Method | Purpose                | Example                    | Response           |
| ------ | ---------------------- | -------------------------- | ------------------ |
| GET    | Retrieve resource(s)   | `GET /api/v1/users`        | 200 + data         |
| POST   | Create new resource    | `POST /api/v1/users`       | 201 + created data |
| PUT    | Update entire resource | `PUT /api/v1/users/123`    | 200 + updated data |
| PATCH  | Partial update         | `PATCH /api/v1/users/123`  | 200 + updated data |
| DELETE | Delete resource        | `DELETE /api/v1/users/123` | 204 (no content)   |

## Route Organization

### Directory Structure

```
src/routes/
├── index.js          # Main route aggregator
├── auth.js           # Authentication routes
├── users.js          # User management routes
├── products.js       # Product routes
└── orders.js         # Order routes
```

### Route File Pattern

Each route file follows this pattern:

```javascript
// src/routes/users.js
const express = require('express');
const userController = require('../controllers/UserController');
const { authenticateToken, requireRole } = require('../middlewares/auth');
const { rateLimiters } = require('../middlewares/rateLimiter');

const router = express.Router();

/**
 * @route GET /api/v1/users
 * @desc Get all users (admin only)
 * @access Private (Admin)
 */
router.get('/', authenticateToken, requireRole('admin'), userController.index);

/**
 * @route GET /api/v1/users/:id
 * @desc Get user by ID
 * @access Private
 */
router.get('/:id', authenticateToken, userController.show);

/**
 * @route POST /api/v1/users
 * @desc Create new user
 * @access Public
 */
router.post('/', rateLimiters.auth, userController.create);

/**
 * @route PUT /api/v1/users/:id
 * @desc Update user
 * @access Private (Self or Admin)
 */
router.put('/:id', authenticateToken, userController.update);

/**
 * @route DELETE /api/v1/users/:id
 * @desc Delete user
 * @access Private (Admin)
 */
router.delete(
  '/:id',
  authenticateToken,
  requireRole('admin'),
  userController.destroy,
);

module.exports = router;
```

## Route Registration

### Main App Registration

Routes are registered in the main app file:

```javascript
// src/app.js
const express = require('express');
const { rateLimiters } = require('./middlewares/rateLimiter');

// Import route modules
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const productRoutes = require('./routes/products');
const orderRoutes = require('./routes/orders');

const app = express();

// Health check endpoint
app.get('/api/v1/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// Register route modules with middleware
app.use('/api/v1/auth', rateLimiters.auth, authRoutes);
app.use('/api/v1/users', rateLimiters.general, userRoutes);
app.use('/api/v1/products', rateLimiters.general, productRoutes);
app.use('/api/v1/orders', rateLimiters.general, orderRoutes);

module.exports = app;
```

### Route Aggregator (Optional)

For larger applications, you can use a route aggregator:

```javascript
// src/routes/index.js
const express = require('express');
const authRoutes = require('./auth');
const userRoutes = require('./users');
const productRoutes = require('./products');

const router = express.Router();

// Health check
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// Route modules
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/products', productRoutes);

module.exports = router;

// In app.js
app.use('/api/v1', require('./routes'));
```

## RESTful Route Examples

### User Routes

```javascript
// src/routes/users.js
const express = require('express');
const userController = require('../controllers/UserController');
const { authenticateToken, requireRole } = require('../middlewares/auth');

const router = express.Router();

// Collection routes
router.get('/', authenticateToken, requireRole('admin'), userController.index);
router.post('/', userController.create);

// Member routes
router.get('/:id', authenticateToken, userController.show);
router.put('/:id', authenticateToken, userController.update);
router.patch('/:id', authenticateToken, userController.update);
router.delete(
  '/:id',
  authenticateToken,
  requireRole('admin'),
  userController.destroy,
);

// Custom routes
router.get('/:id/profile', authenticateToken, userController.profile);
router.post(
  '/:id/change-password',
  authenticateToken,
  userController.changePassword,
);

module.exports = router;
```

### Product Routes with Nested Resources

```javascript
// src/routes/products.js
const express = require('express');
const productController = require('../controllers/ProductController');
const reviewController = require('../controllers/ReviewController');
const {
  authenticateToken,
  requireRole,
  optionalAuth,
} = require('../middlewares/auth');

const router = express.Router();

// Product routes
router.get('/', optionalAuth, productController.index);
router.get('/:id', optionalAuth, productController.show);
router.post(
  '/',
  authenticateToken,
  requireRole('admin'),
  productController.create,
);
router.put(
  '/:id',
  authenticateToken,
  requireRole('admin'),
  productController.update,
);
router.delete(
  '/:id',
  authenticateToken,
  requireRole('admin'),
  productController.destroy,
);

// Nested review routes
router.get('/:productId/reviews', reviewController.index);
router.post('/:productId/reviews', authenticateToken, reviewController.create);
router.put(
  '/:productId/reviews/:id',
  authenticateToken,
  reviewController.update,
);
router.delete(
  '/:productId/reviews/:id',
  authenticateToken,
  reviewController.destroy,
);

// Custom product routes
router.post(
  '/:id/favorite',
  authenticateToken,
  productController.addToFavorites,
);
router.delete(
  '/:id/favorite',
  authenticateToken,
  productController.removeFromFavorites,
);
router.get('/category/:category', productController.byCategory);
router.get('/search', productController.search);

module.exports = router;
```

### Authentication Routes

```javascript
// src/routes/auth.js
const express = require('express');
const authController = require('../controllers/AuthController');
const { authenticateToken } = require('../middlewares/auth');

const router = express.Router();

/**
 * @route POST /api/v1/auth/login
 * @desc Authenticate user and return JWT token
 * @access Public
 */
router.post('/login', authController.login);

/**
 * @route POST /api/v1/auth/register
 * @desc Register new user
 * @access Public
 */
router.post('/register', authController.register);

/**
 * @route POST /api/v1/auth/logout
 * @desc Logout user (invalidate token)
 * @access Private
 */
router.post('/logout', authenticateToken, authController.logout);

/**
 * @route POST /api/v1/auth/refresh
 * @desc Refresh JWT token
 * @access Private
 */
router.post('/refresh', authenticateToken, authController.refresh);

/**
 * @route POST /api/v1/auth/forgot-password
 * @desc Send password reset email
 * @access Public
 */
router.post('/forgot-password', authController.forgotPassword);

/**
 * @route POST /api/v1/auth/reset-password
 * @desc Reset password with token
 * @access Public
 */
router.post('/reset-password', authController.resetPassword);

module.exports = router;
```

## Route Parameters and Query Strings

### Path Parameters

```javascript
// Route definition
router.get('/users/:id/orders/:orderId', orderController.show);

// Controller access
show = this.createMethod(async (req, res) => {
  const { id: userId, orderId } = req.params;
  // userId and orderId are available
});
```

### Query Parameters

```javascript
// Route definition
router.get('/products', productController.index);

// Controller access
index = this.createMethod(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    category,
    minPrice,
    maxPrice,
    search,
    sortBy = 'name',
    sortOrder = 'asc',
  } = req.query;

  // Use query parameters for filtering, pagination, sorting
});

// Example request: GET /api/v1/products?category=electronics&minPrice=100&page=2&limit=20
```

### Request Body

```javascript
// Route definition
router.post('/users', userController.create);

// Controller access
create = this.createMethod(async (req, res) => {
  const input = new CreateUserInput(req.body);
  input.validate();

  const userData = input.output();
  // Process validated request body
});
```

## Route Middleware

### Route-Specific Middleware

```javascript
// Apply middleware to specific routes
router.get(
  '/admin',
  authenticateToken,
  requireRole('admin'),
  controller.adminPanel,
);

// Apply middleware to all routes in router
router.use(authenticateToken); // All subsequent routes require authentication

// Apply middleware to route group
const adminRouter = express.Router();
adminRouter.use(authenticateToken, requireRole('admin'));
adminRouter.get('/users', userController.adminIndex);
adminRouter.get('/reports', reportController.index);

router.use('/admin', adminRouter);
```

### Conditional Middleware

```javascript
// Conditional authentication
const conditionalAuth = (req, res, next) => {
  if (req.path.startsWith('/public')) {
    return next(); // Skip authentication for public routes
  }
  return authenticateToken(req, res, next);
};

router.use(conditionalAuth);
```

## API Versioning

### URL Path Versioning (Current)

```javascript
// v1 routes
app.use('/api/v1', v1Routes);

// v2 routes (when needed)
app.use('/api/v2', v2Routes);
```

### Header Versioning (Alternative)

```javascript
// Version middleware
const versionMiddleware = (req, res, next) => {
  const version = req.headers['api-version'] || 'v1';
  req.apiVersion = version;
  next();
};

// Route based on version
router.get('/users', versionMiddleware, (req, res, next) => {
  if (req.apiVersion === 'v2') {
    return userControllerV2.index(req, res, next);
  }
  return userController.index(req, res, next);
});
```

## Error Handling in Routes

### Route-Level Error Handling

```javascript
// Error handling is automatic with ApiController.createMethod()
router.get('/:id', userController.show); // Errors handled by ApiController

// Manual error handling (not recommended)
router.get('/:id', async (req, res, next) => {
  try {
    const user = await userService.findById(req.params.id);
    res.json({ data: user });
  } catch (error) {
    next(error); // Pass to global error handler
  }
});
```

### 404 Handling

```javascript
// In app.js, after all routes
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method,
  });
});
```

## Route Testing

### Testing Routes

```javascript
// tests/routes/users.test.js
const request = require('supertest');
const app = require('../../src/app');
const { User } = require('../../src/models');

describe('User Routes', () => {
  describe('GET /api/v1/users/:id', () => {
    it('should return user data', async () => {
      const user = await User.create({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      });

      const response = await request(app)
        .get(`/api/v1/users/${user.id}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('id', user.id);
    });

    it('should return 404 for non-existent user', async () => {
      const response = await request(app)
        .get('/api/v1/users/999')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
    });
  });

  describe('POST /api/v1/users', () => {
    it('should create new user', async () => {
      const userData = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'password123',
      };

      const response = await request(app).post('/api/v1/users').send(userData);

      expect(response.status).toBe(201);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.email).toBe(userData.email);
    });
  });
});
```

## Best Practices

### 1. Follow RESTful Conventions

```javascript
// ✅ Good - RESTful routes
GET    /api/v1/users          # List users
POST   /api/v1/users          # Create user
GET    /api/v1/users/:id      # Show user
PUT    /api/v1/users/:id      # Update user (full)
PATCH  /api/v1/users/:id      # Update user (partial)
DELETE /api/v1/users/:id      # Delete user

// ❌ Bad - non-RESTful routes
GET    /api/v1/getUsers
POST   /api/v1/createUser
GET    /api/v1/userDetails/:id
```

### 2. Use Descriptive Route Comments

```javascript
/**
 * @route POST /api/v1/users/:id/change-password
 * @desc Change user password
 * @access Private (Self only)
 * @body {string} currentPassword - Current password
 * @body {string} newPassword - New password
 * @returns {200} Success message
 * @returns {400} Validation error
 * @returns {401} Invalid current password
 */
router.post(
  '/:id/change-password',
  authenticateToken,
  userController.changePassword,
);
```

### 3. Group Related Routes

```javascript
// ✅ Good - grouped by resource
// users.js - all user-related routes
// products.js - all product-related routes
// orders.js - all order-related routes

// ❌ Bad - mixed routes in single file
// routes.js - all routes mixed together
```

### 4. Use Middleware Appropriately

```javascript
// ✅ Good - specific middleware for specific needs
router.get('/public', publicController.index); // No auth needed
router.get('/profile', authenticateToken, userController.profile); // Auth required
router.get(
  '/admin',
  authenticateToken,
  requireRole('admin'),
  adminController.index,
); // Auth + role

// ❌ Bad - unnecessary middleware
router.get('/public', authenticateToken, publicController.index); // Auth not needed
```

### 5. Handle Route Parameters Safely

```javascript
// ✅ Good - validate parameters
show = this.createMethod(async (req, res) => {
  const { id } = req.params;

  // Validate ID format
  if (!id || isNaN(parseInt(id))) {
    throw new InvalidError('Invalid user ID');
  }

  const user = await this.service.findById(parseInt(id));
  // ... rest of logic
});

// ❌ Bad - use parameters directly
show = this.createMethod(async (req, res) => {
  const user = await this.service.findById(req.params.id); // Could be invalid
});
```

### 6. Use Consistent Response Formats

```javascript
// ✅ Good - consistent format via output classes
router.get('/:id', userController.show); // Uses UserOutput for formatting

// ❌ Bad - inconsistent responses
router.get('/:id', (req, res) => {
  const user = await User.findByPk(req.params.id);
  res.json(user); // Raw database object
});
```

### 7. Version Your APIs

```javascript
// ✅ Good - versioned endpoints
app.use('/api/v1', v1Routes);
app.use('/api/v2', v2Routes);

// ❌ Bad - unversioned endpoints
app.use('/api', routes); // No version, hard to maintain
```

### 8. Document Route Dependencies

```javascript
// ✅ Good - clear middleware dependencies
router.get(
  '/profile',
  authenticateToken, // Requires valid JWT
  userController.profile,
);

router.get(
  '/admin',
  authenticateToken, // Requires valid JWT
  requireRole('admin'), // Requires admin role
  adminController.index,
);
```
